"use client";

import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { StatusBar, View, Platform } from "react-native";
import { Provider } from "react-redux";
import store from "./src/store";
import { ThemeProvider } from "./src/theme/ThemeProvider";
import { MaterialIcons } from "@expo/vector-icons";
import { useEffect, useState, useRef } from "react";
import { loadUserData, loadLoginStatus } from "./src/utils/storage";
import { initializeAuth } from "./src/store/authSlice";
import * as Notifications from "expo-notifications";
import {
  registerForPushNotificationsAsync,
  storeNotification,
} from "./src/services/NotificationService";

// Navigation
import TabNavigator from "./src/navigation/TabNavigator";

// Screens
import SplashScreen from "./src/screens/SplashScreen";
import CheckoutScreen from "./src/screens/CheckoutScreen";
import ItemDetailScreen from "./src/screens/ItemDetailScreen";
import SearchScreen from "./src/screens/SearchScreen";
import LoginScreen from "./src/screens/LoginScreen";
import OrderHistoryScreen from "./src/screens/OrderHistoryScreen";
import OrderDetailScreen from "./src/screens/OrderDetailScreen";
import ForgotPasswordScreen from "./src/screens/ForgotPasswordScreen";
import ChangePasswordScreen from "./src/screens/ChangePasswordScreen";
import NotificationSettingsScreen from "./src/screens/NotificationSettingsScreen";
import NotificationsScreen from "./src/screens/NotificationsScreen";
import DeleteAccountScreen from "./src/screens/DeleteAccountScreen";

const Stack = createStackNavigator();

// Set up notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Create a wrapper component to handle the initialization
const AppWithAuth = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [expoPushToken, setExpoPushToken] = useState("");
  const notificationListener = useRef();
  const responseListener = useRef();
  const navigation = useRef();

  useEffect(() => {
    const initializeAuthState = async () => {
      try {
        // Load user data and login status from AsyncStorage
        const userData = await loadUserData();
        const isLoggedIn = await loadLoginStatus();

        // Initialize Redux store with the loaded data
        store.dispatch(initializeAuth({ isLoggedIn, userData }));
      } catch (error) {
        console.error("Error initializing auth state:", error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeAuthState();
  }, []);

  // Set up push notifications
  useEffect(() => {
    // Register for push notifications
    registerForPushNotificationsAsync().then((token) => {
      setExpoPushToken(token);
    });

    // This listener is fired whenever a notification is received while the app is foregrounded
    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        console.log("Notification received:", notification);
        // We don't store the notification here anymore, only when it's clicked
      });

    // This listener is fired whenever a user taps on or interacts with a notification
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener(
        async (response) => {
          console.log("Notification response received:", response);

          // Store the notification when it's tapped
          const storedNotification = await storeNotification(
            response.notification.request.content
          );
          console.log("Stored notification from tap:", storedNotification);

          // Handle navigation based on notification data
          const data = response.notification.request.content.data;

          if (data?.type === "order_update" && data?.orderId) {
            // Navigate to order detail screen
            if (navigation.current) {
              navigation.current.navigate("OrderDetail", {
                orderId: data.orderId,
              });
            }
          } else {
            // Navigate to notifications screen for other notification types
            if (navigation.current) {
              navigation.current.navigate("Notifications");
            }
          }
        }
      );

    return () => {
      // Clean up the listeners
      Notifications.removeNotificationSubscription(
        notificationListener.current
      );
      Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);

  // Show nothing while initializing
  if (!isInitialized) {
    return null;
  }

  return (
    <NavigationContainer
      ref={(navigatorRef) => {
        navigation.current = navigatorRef;
      }}
    >
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === "android" ? "transparent" : "#FFFFFF"}
        translucent={Platform.OS === "android"}
        hidden={false}
      />
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerStyle: {
            backgroundColor: "#000000",
            elevation: 0,
            shadowOpacity: 0,
          },
          headerTintColor: "#FFFFFF",
          headerTitleStyle: {
            fontFamily: "Poppins-SemiBold",
            fontSize: 18,
          },
          headerTitleAlign: "center",
        }}
      >
        <Stack.Screen
          name="Splash"
          component={SplashScreen}
          options={{ headerShown: false }}
        />

        <Stack.Screen
          name="Main"
          component={TabNavigator}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="Checkout"
          component={CheckoutScreen}
          options={{
            headerTitle: "Checkout",
            headerLeft: (props) => (
              <View style={{ marginLeft: 8 }}>
                <MaterialIcons
                  name="arrow-back"
                  size={24}
                  color="#FFFFFF"
                  {...props}
                />
              </View>
            ),
          }}
        />

        <Stack.Screen
          name="ItemDetail"
          component={ItemDetailScreen}
          options={{
            headerShown: false,
            presentation: "card",
          }}
        />
        <Stack.Screen
          name="Search"
          component={SearchScreen}
          options={{
            headerShown: false,
            presentation: "card",
          }}
        />
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ForgotPasswordScreen"
          component={ForgotPasswordScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="OrderHistory"
          component={OrderHistoryScreen}
          options={{
            headerTitle: "Order History",
            headerStyle: {
              backgroundColor: "#FFFFFF",
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: "#000000",
          }}
        />
        <Stack.Screen
          name="OrderDetail"
          component={OrderDetailScreen}
          options={{
            headerTitle: "Order Details",
            headerStyle: {
              backgroundColor: "#FFFFFF",
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: "#000000",
          }}
        />
        <Stack.Screen
          name="ChangePassword"
          component={ChangePasswordScreen}
          options={{
            headerTitle: "Change Password",
            headerStyle: {
              backgroundColor: "#FFFFFF",
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: "#000000",
            headerLeft: (props) => (
              <View style={{ marginLeft: 8 }}>
                <MaterialIcons
                  name="arrow-back"
                  size={24}
                  color="#000000"
                  {...props}
                />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="NotificationSettings"
          component={NotificationSettingsScreen}
          options={{
            headerTitle: "Notification Settings",
            headerStyle: {
              backgroundColor: "#FFFFFF",
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: "#000000",
            headerLeft: (props) => (
              <View style={{ marginLeft: 8 }}>
                <MaterialIcons
                  name="arrow-back"
                  size={24}
                  color="#000000"
                  {...props}
                />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="Notifications"
          component={NotificationsScreen}
          options={{
            headerTitle: "Notifications",
            headerStyle: {
              backgroundColor: "#FFFFFF",
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: "#000000",
            headerLeft: (props) => (
              <View style={{ marginLeft: 8 }}>
                <MaterialIcons
                  name="arrow-back"
                  size={24}
                  color="#000000"
                  {...props}
                />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="DeleteAccount"
          component={DeleteAccountScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppWithAuth />
      </ThemeProvider>
    </Provider>
  );
}
