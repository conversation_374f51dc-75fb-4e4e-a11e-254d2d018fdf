# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
    npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

3. Update the `app.json` file according to the following points

- `name` - The name of your app
- `slug` - The slug of your app
- `version` - The version of your app
- `bundleIdentifier` - The bundle identifier of your app
- `icon` - The icon of your app
- `splash` - The splash screen of your app
- `ios` - The iOS configuration of your app
- `android` - The Android configuration of your app
- `web` - The web configuration of your app
- `extra` - The extra configuration of your app

4. Update the business ID in `src/config/index.ts` to the ID of your business

5. Update the splash screen image in `src/assets/splash.jpg` to the image of your choice

6. Update the icon image in `src/assets/icon.png` to the image of your choice

7. Update the splash screen image in `src/assets/splash-icon.png` to the image of your choice

8. For build creation of new expo project follow these instructions

- Install eas-cli

  ```bash
  npm install -g eas-cli
  ```

  - Configure EAS build

  ```bash
  eas build:configure
  ```

- Login to eas-cli

  ```bash
  eas login
  ```

- Create a new build

  ```bash
  eas build --platform android
  ```

  ```bash
  eas build --platform ios
  ```

  - Provide the path to the .p12 file and the password for the .p12 file when prompted to save .p12 file for ios build

  - Provide the required data for iOS build deployment (Apple Developer Account, Apple Distribution Certificate, Apple Distribution Provisioning Profile, etc)

  - Provide the path to the .jks file and the password for the .jks file when prompted to save .jks file for android build

  9. Note:

  - For iOS build deployment on the TestFlight, an external app `Transporter` is used for now.
  - Just download the .ipa file from the eas build and upload it to the TestFlight using the `Transporter` app.
