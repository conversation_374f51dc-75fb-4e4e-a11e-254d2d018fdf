"use client";

import type React from "react";
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  View,
  type StyleProp,
  type ViewStyle,
  type TextStyle,
} from "react-native";
import { useTheme } from "../theme/ThemeProvider";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "text";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = "left",
  style,
  textStyle,
}) => {
  const { theme, typography, borderRadius } = useTheme();

  // Get button styles based on variant and size
  const getButtonStyle = () => {
    let baseStyle: ViewStyle = {
      borderRadius: borderRadius.lg, // KFC uses more rounded buttons
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
    };

    // Size styles
    switch (size) {
      case "small":
        baseStyle = { ...baseStyle, paddingVertical: 8, paddingHorizontal: 12 };
        break;
      case "large":
        baseStyle = {
          ...baseStyle,
          paddingVertical: 16,
          paddingHorizontal: 24,
        };
        break;
      default: // medium
        baseStyle = {
          ...baseStyle,
          paddingVertical: 12,
          paddingHorizontal: 16,
        };
    }

    // Variant styles
    switch (variant) {
      case "secondary":
        baseStyle = { ...baseStyle, backgroundColor: theme.secondary };
        break;
      case "outline":
        baseStyle = {
          ...baseStyle,
          backgroundColor: "transparent",
          borderWidth: 1,
          borderColor: theme.primary,
        };
        break;
      case "text":
        baseStyle = {
          ...baseStyle,
          backgroundColor: "transparent",
          paddingHorizontal: 4,
          paddingVertical: 4,
        };
        break;
      default: // primary
        baseStyle = { ...baseStyle, backgroundColor: theme.primary };
    }

    // Disabled state
    if (disabled) {
      baseStyle = {
        ...baseStyle,
        backgroundColor:
          variant === "outline" || variant === "text"
            ? "transparent"
            : theme.disabled,
        borderColor: variant === "outline" ? theme.disabled : undefined,
        opacity: variant === "text" ? 0.5 : 1,
      };
    }

    // Full width
    if (fullWidth) {
      baseStyle = { ...baseStyle, width: "100%" };
    }

    return baseStyle;
  };

  // Get text styles based on variant and size
  const getTextStyle = () => {
    let baseStyle: TextStyle = {
      ...typography.button,
      textAlign: "center",
      fontWeight: "bold", // KFC uses bold text for buttons
    };

    // Size-based text styles
    switch (size) {
      case "small":
        baseStyle = { ...baseStyle, fontSize: 12 };
        break;
      case "large":
        baseStyle = { ...baseStyle, fontSize: 16 };
        break;
      default: // medium
        baseStyle = { ...baseStyle, fontSize: 14 };
    }

    // Variant-based text styles
    switch (variant) {
      case "outline":
        baseStyle = { ...baseStyle, color: theme.primary };
        break;
      case "text":
        baseStyle = { ...baseStyle, color: theme.primary };
        break;
      default: // primary and secondary
        baseStyle = { ...baseStyle, color: "#FFFFFF" };
    }

    // Disabled state
    if (disabled) {
      baseStyle = {
        ...baseStyle,
        color: variant,
      };
    }

    // Disabled state
    if (disabled) {
      baseStyle = {
        ...baseStyle,
        color:
          variant === "outline" || variant === "text"
            ? theme.disabled
            : "#FFFFFF",
      };
    }

    return baseStyle;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={
            variant === "outline" || variant === "text"
              ? theme.primary
              : "#FFFFFF"
          }
        />
      ) : (
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          {icon && iconPosition === "left" && (
            <View style={{ marginRight: 8 }}>{icon}</View>
          )}
          <Text style={[getTextStyle(), textStyle]}>{title}</Text>
          {icon && iconPosition === "right" && (
            <View style={{ marginLeft: 8 }}>{icon}</View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default Button;
