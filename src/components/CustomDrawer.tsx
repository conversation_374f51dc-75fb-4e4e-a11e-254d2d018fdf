"use client";

import type React from "react";
import { useRef, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  Image,
  SafeAreaView,
  Platform,
  StatusBar,
  ScrollView,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
// Import useSelector from react-redux
import { useSelector, useDispatch } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { logout } from "../store/authSlice";

const { width } = Dimensions.get("window");
const DRAWER_WIDTH = width * 0.8;

interface CustomDrawerProps {
  visible: boolean;
  onClose: () => void;
}

// Add dispatch and auth state
const CustomDrawer: React.FC<CustomDrawerProps> = ({ visible, onClose }) => {
  const translateX = useRef(new Animated.Value(-DRAWER_WIDTH)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const { logo } = useSelector((state: any) => state.order);
  const { isLoggedIn, userData } = useSelector((state: any) => state.auth);
  const [isRendered, setIsRendered] = useState(false);
  const navigation = useNavigation();
  const dispatch = useDispatch();

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      setIsRendered(true);
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: -DRAWER_WIDTH,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Only remove from DOM after animation completes
        setIsRendered(false);
      });
    }
  }, [visible]);

  // Reset animation values when component unmounts
  useEffect(() => {
    return () => {
      translateX.setValue(-DRAWER_WIDTH);
      opacity.setValue(0);
    };
  }, []);

  // Navigation handlers
  const handleNavigation = (screen) => {
    onClose();
    navigation.navigate(screen);
  };

  // Add handleLogout function
  const handleLogout = () => {
    dispatch(logout());
    onClose();
    navigation.navigate("Home");
  };

  // Don't render anything if not visible and not in the middle of closing animation
  if (!visible && !isRendered) {
    return null;
  }

  return (
    <View style={styles.container} pointerEvents={visible ? "auto" : "none"}>
      {/* Backdrop */}
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View style={[styles.backdrop, { opacity }]} />
      </TouchableWithoutFeedback>

      {/* Drawer Content */}
      <Animated.View
        style={[
          styles.drawer,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <SafeAreaView style={styles.safeArea}>
          {/* Header with logo */}
          <View style={styles.drawerHeader}>
            {logo ? (
              <Image
                source={{ uri: logo }}
                style={styles.logo}
                resizeMode="contain"
              />
            ) : (
              <View style={styles.logoPlaceholder}>
                <MaterialIcons name="restaurant" size={60} color="#000000" />
              </View>
            )}
          </View>

          {/* Drawer Items */}
          <ScrollView style={styles.drawerItems}>
            {isLoggedIn ? (
              <>
                <TouchableOpacity
                  style={styles.drawerItem}
                  onPress={() => handleNavigation("Notifications")}
                >
                  <Text style={styles.drawerItemText}>Notifications</Text>
                </TouchableOpacity>
                <View style={styles.divider} />

                <TouchableOpacity
                  style={styles.drawerItem}
                  onPress={() => handleNavigation("OrderHistory")}
                >
                  <Text style={styles.drawerItemText}>Order History</Text>
                </TouchableOpacity>
                <View style={styles.divider} />

                <TouchableOpacity
                  style={styles.drawerItem}
                  onPress={() => handleNavigation("ChangePassword")}
                >
                  <Text style={styles.drawerItemText}>Change Password</Text>
                </TouchableOpacity>
                <View style={styles.divider} />

                <TouchableOpacity
                  style={styles.drawerItem}
                  onPress={handleLogout}
                >
                  <Text style={[styles.drawerItemText, { color: "#FF3B30" }]}>
                    Logout
                  </Text>
                </TouchableOpacity>
                <View style={styles.divider} />
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.drawerItem}
                  onPress={() => handleNavigation("Login")}
                >
                  <Text style={styles.drawerItemText}>Login</Text>
                </TouchableOpacity>
                <View style={styles.divider} />
              </>
            )}
          </ScrollView>

          {/* Powered by Ordrz */}
          <View style={styles.poweredByContainer}>
            <Text style={styles.poweredByText}>Powered by Ordrz</Text>
          </View>
        </SafeAreaView>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#000",
  },
  drawer: {
    position: "absolute",
    top: 0,
    left: 0,
    width: DRAWER_WIDTH,
    height: "100%",
    backgroundColor: "#FFF",
    shadowColor: "#000",
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
  },
  safeArea: {
    flex: 1,
    paddingTop:
      Platform.OS === "android" ? (StatusBar.currentHeight || 0) + 10 : 0,
  },
  drawerHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
    alignItems: "center",
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 10,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#F5F5F5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#000000",
    fontFamily: "PlusJakartaSans-Bold",
  },
  drawerItems: {
    flex: 1,
  },
  drawerItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  drawerItemText: {
    fontSize: 16,
    color: "#000000",
    fontFamily: "PlusJakartaSans-Medium",
  },
  poweredByContainer: {
    padding: 20,
    alignItems: "center",
    marginBottom: 16,
  },
  poweredByText: {
    fontSize: 14,
    color: "#000000",
    fontFamily: "PlusJakartaSans-Regular",
  },
  userInfoContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
    marginBottom: 8,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#000000",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  userAvatarText: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "PlusJakartaSans-Bold",
  },
  userDetails: {
    marginLeft: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 4,
    fontFamily: "PlusJakartaSans-Bold",
  },
  userEmail: {
    fontSize: 14,
    color: "#666666",
    fontFamily: "PlusJakartaSans-Regular",
  },
  divider: {
    height: 1,
    backgroundColor: "#F0F0F0",
    marginHorizontal: 20,
  },
});

export default CustomDrawer;
