"use client";

import React from "react";
import { View, Text, StyleSheet, Dimensions } from "react-native";
import { useSelector } from "react-redux";

// Utility function to handle hex colors with or without # prefix
const hexToColor = (hex: string): string => {
  // Default color if hex is invalid or empty
  if (!hex) {
    return "#000000";
  }

  // Add # prefix if it doesn't exist
  if (!hex.startsWith("#")) {
    hex = "#" + hex;
  }

  // Validate the hex format (with # prefix)
  if (!/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    return "#000000";
  }

  return hex;
};

interface TickerProps {
  // Optional props for customization
  customStyle?: any;
  // Theme data can be passed directly
  themeData?: any;
}

const Ticker: React.FC<TickerProps> = ({
  customStyle,
  themeData: propThemeData,
}) => {
  // Get theme data from Redux store as fallback
  const { themeData: reduxThemeData } = useSelector(
    (state: any) => state.order
  );

  // Use prop data if available, otherwise use Redux data
  const themeData = propThemeData || reduxThemeData;

  // Default values
  let tickerText = "";
  let tickerBackgroundColor = "#000000";
  let tickerFontColor = "#FFFFFF";
  let tickerOnOff = "0";

  // Parse theme settings if available
  if (themeData) {
    let themeSettings;

    // Case 1: Check if themeData has theme_settings property
    if (themeData.theme_settings) {
      themeSettings = themeData.theme_settings;
    }

    // If we have theme settings, extract ticker data
    if (themeSettings) {
      const parsedTheme = JSON.parse(themeSettings);

      // Check if ticker is enabled
      tickerOnOff = parsedTheme.theme_settings.header_ticker_on_off || "0";
      console.log("Ticker on/off:", tickerOnOff);

      if (tickerOnOff === "1") {
        tickerText = parsedTheme.theme_settings.header_ticker_text || "";
        tickerBackgroundColor =
          parsedTheme.theme_settings.header_ticker_background_color ||
          "#000000";
        tickerFontColor =
          parsedTheme.theme_settings.header_ticker_font_color || "#FFFFFF";
      }
    }
  }

  // Don't render anything if ticker is disabled or text is empty
  if (tickerOnOff !== "1" || !tickerText.trim()) {
    return null;
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: hexToColor(tickerBackgroundColor) },
        customStyle,
      ]}
    >
      <Text style={[styles.text, { color: hexToColor(tickerFontColor) }]}>
        {tickerText}
      </Text>
    </View>
  );
};

const { width } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    width: width,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    fontSize: 14,
    fontWeight: "bold",
    textAlign: "center",
    fontFamily: "PlusJakartaSans-Bold",
  },
});

export default Ticker;
