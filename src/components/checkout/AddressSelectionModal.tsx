"use client";

import { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Platform,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useCartCalculation } from "../../hooks/useCartCalculation";
import { getCartCalculationPayload } from "../../utils/apiConfig";

interface AddressSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (address: AddressData, calculatedCart?: any) => void;
  initialAddress: AddressData;
  deliverySettings: any;
  cartData: any;
  businessId: string;
  branchId: string;
  orderType: string;
  userInfo: any;
}

interface AddressData {
  streetAddress: string;
  area: string;
  city: string;
  country?: string;
  postalCode?: string;
  location?: { lat: string; lng: string };
}

const AddressSelectionModal = ({
  visible,
  onClose,
  onSave,
  initialAddress,
  deliverySettings,
  cartData,
  businessId,
  branchId,
  orderType,
  userInfo,
}: AddressSelectionModalProps) => {
  const [streetAddress, setStreetAddress] = useState(
    initialAddress?.streetAddress || ""
  );
  const [area, setArea] = useState(initialAddress?.area || "");
  const [city, setCity] = useState(initialAddress?.city || "");
  const [cityList, setCityList] = useState<string[]>([]);
  const [areaList, setAreaList] = useState<string[]>([]);
  const [errors, setErrors] = useState({
    streetAddress: false,
    area: false,
    city: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Use the cart calculation hook
  const { getCartCalculationsAPICall, getCartCalculationsLoading } =
    useCartCalculation();

  // Extract city and area lists from delivery settings
  useEffect(() => {
    if (
      deliverySettings?.charges_type === "locality" &&
      deliverySettings?.charges_details?.charges
    ) {
      // Extract unique cities
      const cities = Array.from(
        new Set(
          deliverySettings.charges_details.charges.map(
            (charge: { primary_locality: string }) => charge.primary_locality
          )
        )
      );
      setCityList(cities);

      // Set first city if none selected
      if (cities.length > 0 && !city) {
        setCity(cities[0]);
      }
    }
  }, [deliverySettings, city]);

  // Update area list when city changes
  useEffect(() => {
    if (
      city &&
      deliverySettings?.charges_type === "locality" &&
      deliverySettings?.charges_details?.charges
    ) {
      // Filter areas by selected city
      const selectedCityAreas = deliverySettings.charges_details.charges
        .filter(
          (charge: { primary_locality: string }) =>
            charge.primary_locality === city
        )
        .flatMap((charge: { secondary_locality: string }) =>
          charge.secondary_locality
            .split(",")
            .map((area: string) => area.trim())
        );

      setAreaList(selectedCityAreas.length > 0 ? selectedCityAreas : []);

      // Auto-select area if there's only one available
      if (selectedCityAreas.length === 1 && !area) {
        setArea(selectedCityAreas[0]);
      }
    }
  }, [city, deliverySettings]);

  const handleSave = async () => {
    // Validate fields
    const newErrors = {
      streetAddress: !streetAddress.trim(),
      area: !area.trim(),
      city: !city.trim(),
    };

    setErrors(newErrors);

    // Check if any errors
    if (Object.values(newErrors).some((error) => error)) {
      return;
    }

    setIsLoading(true);
    setErrorMessage("");

    try {
      // Prepare complete address data including location info
      const addressData = {
        streetAddress,
        area,
        city,
        country: initialAddress?.country || "",
        postalCode: initialAddress?.postalCode || "",
        location: initialAddress?.location || { lat: "0", lng: "0" },
      };

      // Create cart data object with all required fields
      const cartDataForCalculation = {
        ...cartData,
        businessId,
        branchId,
        name: userInfo?.name || "",
        email: userInfo?.email || "",
        phone: userInfo?.phone || "",
        userLocation: addressData.location,
        userCountry: addressData.country || "",
        userCity: city,
        userArea: area,
        userPostalCode: addressData.postalCode || "",
      };

      // Prepare payload for cart calculation
      const payload = getCartCalculationPayload(
        cartDataForCalculation,
        "0", // Cash on delivery
        orderType,
        "", // No coupon code
        0 // No tip
      );

      // Log the payload for debugging
      console.log(
        "Cart Calculation Payload from modal:",
        JSON.stringify(payload, null, 2)
      );

      // Call cart calculation API
      const result = await getCartCalculationsAPICall(payload, cartData);

      setIsLoading(false);

      if (result && result.success && result.data) {
        console.log(
          "Cart calculation successful in modal, result:",
          result.data
        );

        // Save address data with calculated cart
        onSave(addressData, result.data);
        onClose();
      } else {
        console.error("Cart calculation failed in modal:", result?.error);
        setErrorMessage(
          result?.error ||
            "Failed to calculate delivery charges. Please try again."
        );
      }
    } catch (error) {
      console.error("Error in handleSave:", error);
      setIsLoading(false);
      setErrorMessage("An error occurred. Please try again.");
    }
  };

  const capitalizeFirstLetter = (text: string) => {
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const renderCitySelector = () => {
    if (cityList.length > 0) {
      return (
        <View style={styles.dropdownContainer}>
          <Text style={styles.label}>City*</Text>
          <ScrollView
            style={styles.dropdown}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="handled"
          >
            {cityList.map((cityItem, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dropdownItem,
                  city === cityItem && styles.selectedItem,
                ]}
                onPress={() => {
                  setCity(cityItem);
                  setArea(""); // Reset area when city changes
                  setErrors({ ...errors, city: false });
                }}
              >
                <Text style={styles.dropdownItemText}>
                  {capitalizeFirstLetter(cityItem)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      );
    } else {
      return (
        <View style={styles.inputContainer}>
          <Text style={styles.label}>City*</Text>
          <TextInput
            style={[styles.input, errors.city && styles.inputError]}
            placeholder="Enter city"
            value={city}
            onChangeText={(text) => {
              setCity(text);
              setErrors({ ...errors, city: false });
            }}
          />
        </View>
      );
    }
  };

  const renderAreaSelector = () => {
    if (areaList.length > 0) {
      return (
        <View style={styles.dropdownContainer}>
          <Text style={styles.label}>Area*</Text>
          <ScrollView
            style={styles.dropdown}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="handled"
          >
            {areaList.map((areaItem, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dropdownItem,
                  area === areaItem && styles.selectedItem,
                ]}
                onPress={() => {
                  setArea(areaItem);
                  setErrors({ ...errors, area: false });
                }}
              >
                <Text style={styles.dropdownItemText}>
                  {capitalizeFirstLetter(areaItem)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      );
    } else {
      return (
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Area*</Text>
          <TextInput
            style={[styles.input, errors.area && styles.inputError]}
            placeholder="Enter area"
            value={area}
            onChangeText={(text) => {
              setArea(text);
              setErrors({ ...errors, area: false });
            }}
          />
        </View>
      );
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Address</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialIcons name="close" size={24} color="#000" />
          </TouchableOpacity>
        </View>

        {/* Address Preview */}
        {(streetAddress || area || city) && (
          <View style={styles.addressPreview}>
            <Text style={styles.addressPreviewText}>
              {[
                capitalizeFirstLetter(streetAddress),
                capitalizeFirstLetter(area),
                capitalizeFirstLetter(city),
              ]
                .filter(Boolean)
                .join(", ")}
            </Text>
          </View>
        )}

        <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
          {/* Street Address */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Street address*</Text>
            <TextInput
              style={[styles.input, errors.streetAddress && styles.inputError]}
              placeholder="Enter street address"
              value={streetAddress}
              onChangeText={(text) => {
                setStreetAddress(text);
                setErrors({ ...errors, streetAddress: false });
              }}
            />
          </View>

          {/* City and Area Selection */}
          <View style={styles.rowContainer}>
            {renderCitySelector()}
            {renderAreaSelector()}
          </View>

          {/* Error Message */}
          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}
        </ScrollView>

        {/* Footer with Save Button */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSave}
            disabled={isLoading || getCartCalculationsLoading}
          >
            {isLoading || getCartCalculationsLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Add Address</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#fff",
    ...Platform.select({
      ios: {
        paddingTop: 50,
      },
    }),
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  addressPreview: {
    padding: 16,
    backgroundColor: "#f9f9f9",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  addressPreviewText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  inputContainer: {
    marginBottom: 16,
    flex: 1,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: "#333",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  rowContainer: {
    flexDirection: "column",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  dropdownContainer: {
    marginBottom: 16,
    flex: 1,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 4,
    maxHeight: 150,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedItem: {
    backgroundColor: "#f0f7ff",
  },
  dropdownItemText: {
    fontSize: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    backgroundColor: "#fff",
  },
  saveButton: {
    backgroundColor: "#000000",
    padding: 16,
    borderRadius: 80,
    alignItems: "center",
    marginBottom: 16,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    color: "#FF3B30",
    marginBottom: 16,
  },
});

export default AddressSelectionModal;
