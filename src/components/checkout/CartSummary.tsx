"use client";

import { useState, useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { formatPrice } from "../../utils/cartUtils"; // Import the formatPrice function

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface CartSummaryProps {
  cartItems: CartItem[];
  subtotal: number;
  tax: number;
  total: number;
  currencyCode: string;
  deliveryCharges?: number;
  discount?: number;
  couponDiscount?: number;
  tip?: number;
}

const CartSummary = ({
  cartItems,
  subtotal,
  tax,
  total,
  currencyCode,
  deliveryCharges = 0,
  discount = 0,
  couponDiscount = 0,
  tip = 0,
}: CartSummaryProps) => {
  // Log props changes for debugging
  useEffect(() => {
    console.log("CartSummary received props:", {
      subtotal,
      tax,
      total,
      deliveryCharges,
      discount,
      couponDiscount,
      tip,
    });
  }, [subtotal, tax, total, deliveryCharges, discount, couponDiscount, tip]);

  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={styles.card}>
      {/* Order Summary Section */}
      <View style={styles.headerContainer}>
        <Text style={styles.cardTitle}>Order Summary</Text>
        <TouchableOpacity onPress={toggleExpand} style={styles.expandButton}>
          <MaterialIcons
            name={isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
            size={24}
            color="#000000"
          />
        </TouchableOpacity>
      </View>

      {/* Cart Items - Collapsible */}
      {isExpanded && (
        <View style={styles.itemsContainer}>
          {cartItems.map((item) => (
            <View key={item.id} style={styles.itemRow}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemQuantity}>{item.quantity}x</Text>
                <Text style={styles.itemName}>{item.name}</Text>
              </View>
              <Text style={styles.itemPrice}>
                {formatPrice(item.price, currencyCode)}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Divider */}
      <View style={styles.divider} />

      {/* Order Total Section - Always Visible */}
      <View style={styles.totalSection}>
        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Subtotal</Text>
          <Text style={styles.totalValue}>
            {formatPrice(subtotal, currencyCode)}
          </Text>
        </View>

        {tax > 0 && (
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Tax</Text>
            <Text style={styles.totalValue}>
              {formatPrice(tax, currencyCode)}
            </Text>
          </View>
        )}

        {deliveryCharges > 0 && (
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Delivery Charges</Text>
            <Text style={styles.totalValue}>
              {formatPrice(deliveryCharges, currencyCode)}
            </Text>
          </View>
        )}

        {discount > 0 && (
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Discount</Text>
            <Text style={styles.totalValue}>
              -{formatPrice(discount, currencyCode)}
            </Text>
          </View>
        )}

        {couponDiscount > 0 && (
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Coupon Discount</Text>
            <Text style={styles.totalValue}>
              -{formatPrice(couponDiscount, currencyCode)}
            </Text>
          </View>
        )}

        {tip > 0 && (
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Tip</Text>
            <Text style={styles.totalValue}>
              {formatPrice(tip, currencyCode)}
            </Text>
          </View>
        )}

        <View style={[styles.totalRow, styles.grandTotalRow]}>
          <Text style={styles.grandTotalLabel}>Total</Text>
          <Text style={styles.grandTotalValue}>
            {formatPrice(total, currencyCode)}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#F0F0F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000000",
  },
  expandButton: {
    padding: 4,
  },
  itemsContainer: {
    marginBottom: 16,
  },
  itemRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  itemInfo: {
    flexDirection: "row",
    flex: 1,
  },
  itemQuantity: {
    fontSize: 14,
    color: "#666666",
    marginRight: 8,
  },
  itemName: {
    fontSize: 14,
    color: "#000000",
    flex: 1,
  },
  itemPrice: {
    fontSize: 14,
    color: "#000000",
    fontWeight: "500",
  },
  divider: {
    height: 1,
    backgroundColor: "#F0F0F0",
    marginBottom: 16,
  },
  totalSection: {
    gap: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalLabel: {
    fontSize: 14,
    color: "#666666",
  },
  totalValue: {
    fontSize: 14,
    color: "#000000",
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
  },
  grandTotalLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
  },
  grandTotalValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
  },
});

export default CartSummary;
