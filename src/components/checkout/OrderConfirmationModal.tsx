import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

interface OrderConfirmationModalProps {
  visible: boolean;
  onClose: () => void;
  orderId?: string; // Add orderId prop (optional)
}

const OrderConfirmationModal = ({
  visible,
  onClose,
  orderId,
}: OrderConfirmationModalProps) => {
  const navigation = useNavigation();

  const handleBackToHome = () => {
    onClose();
    // Reset the entire navigation stack and make Home the only screen
    navigation.reset({
      index: 0,
      routes: [{ name: "Home" }],
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <View style={styles.cartIcon}>
              <MaterialIcons name="shopping-cart" size={40} color="#000000" />
              <View style={styles.checkmarkContainer}>
                <MaterialIcons name="check" size={24} color="#000000" />
              </View>
            </View>
          </View>

          <Text style={styles.title}>Order successfully placed!</Text>
          <Text style={styles.subtitle}>
            Sit and relax while your order is being prepared.
          </Text>

          {orderId && (
            <Text style={styles.orderIdText}>Order ID: #{orderId}</Text>
          )}

          <TouchableOpacity style={styles.button} onPress={handleBackToHome}>
            <Text style={styles.buttonText}>BACK TO HOME</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    width: "100%",
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  iconContainer: {
    marginBottom: 40,
    alignItems: "center",
  },
  cartIcon: {
    width: 80,
    height: 80,
    borderWidth: 3,
    borderColor: "#000000",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  checkmarkContainer: {
    position: "absolute",
    bottom: 10,
    right: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
    color: "#000000",
  },
  subtitle: {
    fontSize: 16,
    color: "#666666",
    textAlign: "center",
    marginBottom: 16, // Reduced from 40 to accommodate order ID
    paddingHorizontal: 20,
  },
  orderIdText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
    textAlign: "center",
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  button: {
    backgroundColor: "#000000",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 50,
    width: "100%",
    alignItems: "center",
    position: "absolute",
    bottom: 40,
    left: 24,
    right: 24,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default OrderConfirmationModal;
