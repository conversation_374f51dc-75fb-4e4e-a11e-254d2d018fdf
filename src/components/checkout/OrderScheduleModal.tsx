"use client";

import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  SafeAreaView,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import moment from "moment";

interface OrderScheduleModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (schedule: { date: string; time: string }) => void;
  initialSchedule: { date: string; time: string } | null;
  pickupSlots: string[];
  isLoading: boolean;
}

const OrderScheduleModal = ({
  visible,
  onClose,
  onSave,
  initialSchedule,
  pickupSlots,
  isLoading,
}: OrderScheduleModalProps) => {
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [availableDates, setAvailableDates] = useState<string[]>([]);

  // Initialize with available dates (7 days from today)
  useEffect(() => {
    const dates = [];
    const today = moment();

    for (let i = 0; i < 7; i++) {
      const date = moment(today).add(i, "days").format("YYYY-MM-DD");
      dates.push(date);
    }

    setAvailableDates(dates);

    // Set initial values if provided
    if (initialSchedule) {
      setSelectedDate(initialSchedule.date || dates[0]);
      setSelectedTime(initialSchedule.time || "");
    } else {
      setSelectedDate(dates[0]);
      setSelectedTime("");
    }
  }, [initialSchedule]);

  // Handle date selection
  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setSelectedTime(""); // Reset time when date changes
  };

  // Handle time selection
  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  // Handle save button
  const handleSave = () => {
    if (selectedDate && selectedTime) {
      onSave({ date: selectedDate, time: selectedTime });
      onClose();
    }
  };

  // Format date for display
  const formatDate = (date: string) => {
    const momentDate = moment(date);
    const isToday = momentDate.isSame(moment(), "day");
    const isTomorrow = momentDate.isSame(moment().add(1, "day"), "day");

    let dayText = momentDate.format("dddd");
    if (isToday) dayText = "Today";
    else if (isTomorrow) dayText = "Tomorrow";

    return {
      day: dayText,
      date: momentDate.format("D MMM"),
    };
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Schedule pickup</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
          </View>

          {/* Date Selection - Scrollable */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.dateScrollContainer}
            contentContainerStyle={styles.dateContainer}
          >
            {availableDates.map((date) => {
              const formattedDate = formatDate(date);
              const isSelected = date === selectedDate;

              return (
                <TouchableOpacity
                  key={date}
                  style={[
                    styles.dateItem,
                    isSelected && styles.selectedDateItem,
                  ]}
                  onPress={() => handleDateSelect(date)}
                >
                  <Text style={styles.dateDay}>{formattedDate.day}</Text>
                  <Text style={styles.dateDate}>{formattedDate.date}</Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          {/* Time Selection */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#E53935" />
              <Text style={styles.loadingText}>Loading available times...</Text>
            </View>
          ) : pickupSlots.length > 0 ? (
            <ScrollView style={styles.timeListContainer}>
              {pickupSlots.map((time) => (
                <TouchableOpacity
                  key={time}
                  style={styles.timeItem}
                  onPress={() => handleTimeSelect(time)}
                >
                  <Text style={styles.timeText}>{time}</Text>
                  <View
                    style={[
                      styles.radioButton,
                      selectedTime === time && styles.radioButtonSelected,
                    ]}
                  >
                    {selectedTime === time && (
                      <View style={styles.radioButtonInner} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.noTimesContainer}>
              <MaterialIcons name="event-busy" size={48} color="#999" />
              <Text style={styles.noTimesText}>
                No pickup times available for this date
              </Text>
              <Text style={styles.noTimesSubtext}>
                Please select another date
              </Text>
            </View>
          )}

          {/* Save Button */}
          <TouchableOpacity
            style={[
              styles.saveButton,
              !(selectedDate && selectedTime) && styles.disabledButton,
            ]}
            onPress={handleSave}
            disabled={!(selectedDate && selectedTime)}
          >
            <Text style={styles.saveButtonText}>Save Schedule</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    width: "90%",
    maxWidth: 400,
    maxHeight: "80%",
    padding: 0,
    overflow: "hidden",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  dateScrollContainer: {
    maxHeight: 100,
  },
  dateContainer: {
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
  dateItem: {
    width: 120,
    padding: 12,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    alignItems: "center",
    marginHorizontal: 4,
  },
  selectedDateItem: {
    borderColor: "#000",
  },
  dateDay: {
    fontSize: 14,
    fontWeight: "500",
  },
  dateDate: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  timeListContainer: {
    maxHeight: 300,
    paddingHorizontal: 16,
  },
  timeItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  timeText: {
    fontSize: 16,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#999",
    justifyContent: "center",
    alignItems: "center",
  },
  radioButtonSelected: {
    borderColor: "#000",
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#000",
  },
  loadingContainer: {
    padding: 24,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 8,
    color: "#666",
  },
  noTimesContainer: {
    padding: 24,
    alignItems: "center",
  },
  noTimesText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  noTimesSubtext: {
    marginTop: 8,
    fontSize: 14,
    color: "#999",
    textAlign: "center",
  },
  saveButton: {
    backgroundColor: "#000000",
    padding: 16,
    margin: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default OrderScheduleModal;
