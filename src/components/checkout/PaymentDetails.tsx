import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";

interface PaymentMethod {
  id: string;
  name: string;
  isDefault: boolean;
}

interface PaymentDetailsProps {
  paymentMethods: PaymentMethod[];
  selectedPaymentMethod: string;
  setSelectedPaymentMethod: (method: string) => void;
  orderNotes: string;
  setOrderNotes: (notes: string) => void;
}

const PaymentDetails = ({
  paymentMethods,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  orderNotes,
  setOrderNotes,
}: PaymentDetailsProps) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Payment Method</Text>

      {paymentMethods.map((method) => (
        <TouchableOpacity
          key={method.id}
          style={styles.paymentOption}
          onPress={() => setSelectedPaymentMethod(method.id)}
        >
          <View style={styles.radioContainer}>
            <View
              style={[
                styles.radioOuter,
                selectedPaymentMethod === method.id &&
                  styles.radioOuterSelected,
              ]}
            >
              {selectedPaymentMethod === method.id && (
                <View style={styles.radioInner} />
              )}
            </View>
            <Text style={styles.paymentMethodName}>{method.name}</Text>
          </View>

          {method.id === "cash" && (
            <MaterialIcons name="payments" size={24} color="#666" />
          )}

          {method.id === "card" && (
            <MaterialIcons name="credit-card" size={24} color="#666" />
          )}
        </TouchableOpacity>
      ))}

      <View style={styles.notesContainer}>
        <Text style={styles.notesLabel}>Order Notes (Optional)</Text>
        <TextInput
          style={styles.notesInput}
          placeholder="Add notes for your order..."
          placeholderTextColor="#999"
          multiline
          numberOfLines={3}
          value={orderNotes}
          onChangeText={setOrderNotes}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  paymentOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  radioContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  radioOuter: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#ccc",
    alignItems: "center",
    justifyContent: "center",
  },
  radioOuterSelected: {
    borderColor: "#0066cc",
  },
  radioInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: "#0066cc",
  },
  paymentMethodName: {
    marginLeft: 10,
    fontSize: 16,
    color: "#333",
  },
  notesContainer: {
    marginTop: 16,
  },
  notesLabel: {
    fontSize: 16,
    color: "#333",
    marginBottom: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 4,
    padding: 10,
    fontSize: 14,
    color: "#333",
    minHeight: 80,
    textAlignVertical: "top",
  },
});

export default PaymentDetails;
