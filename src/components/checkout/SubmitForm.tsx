import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { useSelector } from "react-redux";
import { useCreateOrder } from "../../hooks/useCreateOrder";
import { createOrderPayload } from "../../utils/apiConfig";
import {
  orderValidation,
  cashPaymentMessage,
  clearStorage,
} from "../../utils/helperFunctions";

const SubmitForm = ({ styles }: { styles?: any }) => {
  // Get the createOrder hook
  const { loading, createOrderAPICall } = useCreateOrder();

  // Get state from Redux
  const state = useSelector((state: any) => state);
  const checkoutDetails = state.order;
  const businessDetails = state.business?.businessDetails;
  const branchDetails = state.business?.branchDetails;

  // Handle place order button click
  const handlePlaceOrder = async () => {
    console.log("Placing order...");

    // Validate order details
    if (!orderValidation(checkoutDetails, businessDetails, branchDetails)) {
      return;
    }

    // Create order payload
    const orderPayload = createOrderPayload(checkoutDetails, {
      name: checkoutDetails.name,
      email: checkoutDetails.email,
      phone: checkoutDetails.phone,
      city: checkoutDetails.userCity,
      area: checkoutDetails.userArea,
      address: checkoutDetails.address,
      notes: checkoutDetails.specialInstructions,
      paymentMethod: checkoutDetails.paymentType?.value,
      orderType: checkoutDetails.orderType,
    });

    console.log("Order payload:", JSON.stringify(orderPayload, null, 2));

    // Call API to create order
    const orderId = await createOrderAPICall(orderPayload);

    // Handle successful order creation
    if (orderId) {
      console.log("Order created successfully with ID:", orderId);

      // Show success message
      cashPaymentMessage(orderId);

      // Clear cart data
      clearStorage();

      // Navigate to order confirmation or success screen
      // This would depend on your navigation setup
      // navigation.navigate('OrderSuccess', { orderId })
    }
  };

  return (
    <View style={[styles, localStyles.container]}>
      <TouchableOpacity
        style={localStyles.button}
        onPress={handlePlaceOrder}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={localStyles.buttonText}>Place Order</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const localStyles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#FFFFFF",
  },
  button: {
    backgroundColor: "#141414",
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default SubmitForm;
