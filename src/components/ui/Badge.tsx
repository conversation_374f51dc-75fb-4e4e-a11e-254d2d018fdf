"use client"

import type React from "react"
import { View, Text, StyleSheet, type StyleProp, type ViewStyle, type TextStyle } from "react-native"
import { useTheme } from "../../theme/ThemeProvider"

interface BadgeProps {
  label: string
  variant?: "primary" | "secondary" | "success" | "warning" | "error" | "info"
  size?: "small" | "medium" | "large"
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
}

export const Badge: React.FC<BadgeProps> = ({ label, variant = "primary", size = "medium", style, textStyle }) => {
  const { theme, typography, borderRadius } = useTheme()

  // Get background color based on variant
  const getBackgroundColor = () => {
    switch (variant) {
      case "secondary":
        return theme.secondary
      case "success":
        return theme.success
      case "warning":
        return theme.warning
      case "error":
        return theme.error
      case "info":
        return "#3498db" // Info color
      default: // primary
        return theme.primary
    }
  }

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          container: {
            paddingVertical: 2,
            paddingHorizontal: 6,
            borderRadius: borderRadius.xs,
          },
          text: {
            ...typography.caption,
            fontSize: 10,
          },
        }
      case "large":
        return {
          container: {
            paddingVertical: 6,
            paddingHorizontal: 12,
            borderRadius: borderRadius.md,
          },
          text: {
            ...typography.body2,
            fontSize: 14,
          },
        }
      default: // medium
        return {
          container: {
            paddingVertical: 4,
            paddingHorizontal: 8,
            borderRadius: borderRadius.sm,
          },
          text: {
            ...typography.caption,
            fontSize: 12,
          },
        }
    }
  }

  const sizeStyles = getSizeStyles()

  return (
    <View style={[styles.container, { backgroundColor: getBackgroundColor() }, sizeStyles.container, style]}>
      <Text style={[styles.text, sizeStyles.text, textStyle]}>{label}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignSelf: "flex-start",
  },
  text: {
    color: "#FFFFFF",
    fontWeight: "500",
  },
})

