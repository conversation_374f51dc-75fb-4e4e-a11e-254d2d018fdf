"use client"

import type React from "react"
import { View, StyleSheet } from "react-native"
import { useTheme } from "../../theme/ThemeProvider"

// Define prop types without using union types directly
interface CardProps {
  children: React.ReactNode
  style?: any
  elevation?: number
  padding?: string
  borderRadius?: string
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  elevation = 1,
  padding = "medium",
  borderRadius = "medium",
}) => {
  const { theme, borderRadius: themeRadius } = useTheme()

  // Get elevation style
  const getElevationStyle = () => {
    switch (elevation) {
      case 0:
        return {
          borderWidth: 1,
          borderColor: theme.border,
          shadowOpacity: 0,
        }
      case 1:
        return {
          shadowColor: theme.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        }
      case 2:
        return {
          shadowColor: theme.shadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 6,
          elevation: 4,
        }
      case 3:
        return {
          shadowColor: theme.shadow,
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.2,
          shadowRadius: 10,
          elevation: 8,
        }
      default:
        return {}
    }
  }

  // Get padding style
  const getPaddingStyle = () => {
    switch (padding) {
      case "none":
        return { padding: 0 }
      case "small":
        return { padding: 4 }
      case "large":
        return { padding: 16 }
      default: // medium
        return { padding: 8 }
    }
  }

  // Get border radius style
  const getBorderRadiusStyle = () => {
    switch (borderRadius) {
      case "none":
        return { borderRadius: 0 }
      case "small":
        return { borderRadius: themeRadius.sm }
      case "large":
        return { borderRadius: themeRadius.lg }
      default: // medium
        return { borderRadius: themeRadius.md }
    }
  }

  return (
    <View
      style={[
        styles.card,
        { backgroundColor: theme.card },
        getElevationStyle(),
        getPaddingStyle(),
        getBorderRadiusStyle(),
        style,
      ]}
    >
      {children}
    </View>
  )
}

const styles = StyleSheet.create({
  card: {
    overflow: "hidden",
    minHeight: 80, // Lower minimum height
    maxHeight: 200, // Higher maximum height for more content
  },
})

