"use client";

import { useState } from "react";
import axios from "axios";
import { getCartCalculations } from "../utils/apiConfig";

export const useCartCalculation = () => {
  const [getCartCalculationsLoading, setGetCartCalculationsLoading] =
    useState(false);

  const getCartCalculationsAPICall = async (payload, cartData) => {
    try {
      setGetCartCalculationsLoading(true);
      console.log(
        "Starting cart calculation API call with payload:",
        JSON.stringify(payload, null, 2)
      );

      // Get the API configuration
      const config = getCartCalculations();

      // Make the API call - ensure we're sending the correct data structure
      // The API expects the payload.data object directly
      const response = await axios.post(config.url, payload.data);

      console.log("Cart calculation API response:", response.data);

      if (
        response.data &&
        response.data.status === 200 &&
        response.data.result
      ) {
        console.log("Cart calculation success, result:", response.data.result);

        // Return success with the data
        return {
          success: true,
          data: response.data.result,
        };
      } else {
        console.error("Cart calculation API error:", response.data);

        // Return failure with error message
        return {
          success: false,
          error: response.data?.message || "Failed to calculate cart.",
        };
      }
    } catch (error) {
      console.error("Error in getCartCalculationsAPICall:", error);

      // Log more detailed error information
      if (error.response?.data) {
        console.error("Error details:", error.response.data);

        // If there's a validation error, log the specific field that failed
        if (
          error.response.data.message &&
          error.response.data.message.includes("Validation Error")
        ) {
          console.error(
            "Validation error detected. Check the data types in the payload."
          );

          // Log a sample of the payload for debugging
          if (
            payload &&
            payload.data &&
            payload.data.items &&
            payload.data.items.length > 0
          ) {
            const sampleItem = payload.data.items[0];
            console.error("Sample item from payload:", {
              menu_item_id: sampleItem.menu_item_id,
              item_category_id: sampleItem.item_category_id,
              item_brand_id: sampleItem.item_brand_id,
              price: sampleItem.price,
              qty: sampleItem.qty,
              types: {
                item_category_id: typeof sampleItem.item_category_id,
                item_brand_id: typeof sampleItem.item_brand_id,
              },
            });
          }
        }
      } else {
        console.error("Error message:", error.message);
      }

      // Return failure with error message
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "An error occurred while calculating cart.",
      };
    } finally {
      setGetCartCalculationsLoading(false);
    }
  };

  return {
    getCartCalculationsAPICall,
    getCartCalculationsLoading,
  };
};

export default useCartCalculation;
