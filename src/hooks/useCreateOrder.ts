"use client";

import { useState, useCallback } from "react";
import { createOrder } from "../utils/apiConfig";

export const useCreateOrder = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const createOrderAPICall = useCallback(async (payload: any) => {
    try {
      setLoading(true);

      // Get business ID from Redux store
      const state = require("../store").store.getState();
      const businessId = state.order.businessId;

      const config = createOrder(businessId);

      // Use fetch instead of axios
      const response = await fetch(config.url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload.data),
      });

      const responseData = await response.json();

      setData(responseData);
      setLoading(false);

      if (responseData?.status === 200 && responseData.result) {
        return responseData.result.order_id;
      }

      return 0;
    } catch (err: any) {
      setError(err);
      setLoading(false);
      return 0;
    }
  }, []);

  return {
    data,
    loading,
    error,
    createOrderAPICall,
  };
};
