"use client";

import { useState, useCallback } from "react";
import { getBusinessData } from "../utils/apiConfig";
import { useDispatch, useSelector } from "react-redux";
import { setOrderType, setPaymentMethods } from "../store/orderSlice/index";

export const useGetBusinessDetails = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const dispatch = useDispatch();

  // Get state from Redux
  const state = useSelector((state) => state);
  const businessId = state.order?.businessId;
  const branchId = state.order?.selectedBranch?.branch_id;
  const orderType = state.order?.orderType;

  const getBusinessDetailsAPICall = useCallback(async () => {
    try {
      setLoading(true);

      console.log("Fetching business details with businessId:", businessId);
      console.log("Fetching business details with branchId:", branchId);

      // Get config for API call
      const config = getBusinessData(businessId, branchId);

      // Use fetch instead of axios
      const response = await fetch(config.url);
      const responseData = await response.json();

      setData(responseData);

      // Verify API response
      if (responseData?.status === 200 && responseData?.result) {
        const {
          payment_options,
          branches,
          currencycode,
          decimal_places,
          settings,
        } = responseData.result;

        // Extract payment methods
        const paymentMethodsList =
          payment_options
            ?.filter((paymentOption) => paymentOption.enabled)
            ?.map((paymentOption) => ({
              label:
                paymentOption?.method === "cod" ? "Cash" : paymentOption?.name,
              value: paymentOption?.method === "cod" ? "0" : "1",
              type: paymentOption?.method,
              accountId: paymentOption?.account_id,
              accountDetails: paymentOption?.account_details || null,
            })) || [];

        // Extract branch details
        const branchDetails = branches?.[0] || {};
        const { delivery, pickup, delivery_settings, time_zone } =
          branchDetails;

        // Parse delivery settings if available
        const deliverySettings = delivery_settings
          ? JSON.parse(delivery_settings)
          : null;

        // Determine available order types
        const orderTypeList = [];
        if (!orderType) {
          if (delivery) orderTypeList.push("delivery");
          if (pickup) orderTypeList.push("pickup");
        }

        // Update Redux store with payment methods and order types
        dispatch(setPaymentMethods(paymentMethodsList));

        // Set order type if not already set
        if (orderTypeList.length > 0 && !orderType) {
          dispatch(setOrderType(orderTypeList[0]));
        }

        // Return business details for use in component
        return {
          paymentMethods: paymentMethodsList,
          orderType: orderTypeList[0] || orderType,
          deliverySettings,
          timeZone: time_zone,
          currencyCode: currencycode,
          decimalPlaces: decimal_places || 0,
          futureOrder: settings?.future_order || 0,
          branchDetails,
        };
      }

      setLoading(false);
      return false;
    } catch (err) {
      console.error("Error fetching business details:", err);
      setError(err);
      setLoading(false);
      return false;
    }
  }, [businessId, branchId, orderType, dispatch]);

  return {
    getBusinessDetailsData: data,
    loading,
    error,
    getBusinessDetailsAPICall,
  };
};
