"use client";

import { useState, useCallback } from "react";
import axios from "axios";
import { getPickupSlots } from "../utils/apiConfig";

export const useGetPickupHours = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to call get pickup hours API
  const getPickupHoursAPICall = useCallback(async (params) => {
    try {
      setLoading(true);
      setError(null);

      // Extract branch ID from params
      const branchId = params.bid || "";

      // Get the API configuration with the branch ID
      const config = getPickupSlots(branchId);

      // Make the API call
      const response = await axios(config);

      // Verify API response
      if (
        response.data?.status === "200" &&
        Array.isArray(response.data.slots)
      ) {
        setData(response.data);
        return response.data.slots;
      }

      setError("Invalid response format");
      return [];
    } catch (error) {
      console.error("Error fetching pickup hours:", error);
      setError(error);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    data,
    loading,
    error,
    getPickupHoursAPICall,
  };
};
