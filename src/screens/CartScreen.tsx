"use client";

import type React from "react";
import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  Alert,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons"; // For close icon
import { useNavigation } from "@react-navigation/native";
import { useSelector, useDispatch } from "react-redux";
import type { StackNavigationProp } from "@react-navigation/stack";
import { setCartData } from "../store/orderSlice/index";
import { API_ENDPOINTS } from "../config"; // Import API endpoints from config
import { formatPrice, getCurrencySymbol } from "../utils/cartUtils";
import { checkBusinessHours } from "../utils/businessHoursUtils";
import { Platform, ToastAndroid } from "react-native";

const { width, height } = Dimensions.get("window");

export type RootStackParamList = {
  Home: undefined;
  Cart: undefined;
  Checkout: undefined;
};

// Define types for the Redux state
interface RootState {
  order: {
    cart: any;
    currency: string;
    businessId: string;
    uniqueOrderId: string;
    selectedBranch: {
      branch_id: string;
    };
    orderType: string;
    allItems: any[];
    businessHours: any;
  };
  globalSettings: {
    minimumSpend: string;
  };
}

interface CartScreenProps {
  visible: boolean;
  onClose: (isEmpty?: boolean) => void;
  onProceedToCheckout: () => void;
}

const CartScreen: React.FC<CartScreenProps> = ({
  visible,
  onClose,
  onProceedToCheckout,
}) => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const dispatch = useDispatch();
  // Track loading state by item ID and action type
  const [loadingItems, setLoadingItems] = useState<
    Record<string, { add?: boolean; sub?: boolean; delete?: boolean }>
  >({});
  const [isCartLoading, setIsCartLoading] = useState(false);
  const [rawCartData, setRawCartData] = useState<any>(null);
  const [wasCartEmpty, setWasCartEmpty] = useState(false);
  // Track which cart items have expanded details
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );

  // Access data from Redux store
  const { cart } = useSelector((state: RootState) => state.order);
  const currency = useSelector((state: RootState) => state.order.currency);
  const businessId = useSelector((state: RootState) => state.order.businessId);
  const uniqueOrderId = useSelector(
    (state: RootState) => state.order.uniqueOrderId
  );
  const orderType = useSelector((state: RootState) => state.order.orderType);
  const branchId = useSelector(
    (state: RootState) => state.order.selectedBranch?.branch_id
  );
  const allItems = useSelector((state: RootState) => state.order.allItems);
  const businessHours = useSelector(
    (state: RootState) => state.order.businessHours
  );
  const minimumSpend = useSelector(
    (state: RootState) => state.globalSettings.minimumSpend
  );

  // Function to fetch cart data from API
  const fetchCartData = useCallback(async () => {
    if (!businessId || !uniqueOrderId) return;

    console.log("business ID: ", businessId);
    console.log("unique order ID: ", uniqueOrderId);

    setIsCartLoading(true);
    try {
      const response = await fetch(
        `${API_ENDPOINTS.CART}/${businessId}/cart/${uniqueOrderId}`
      );

      if (!response.ok) {
        throw new Error(`Error fetching cart: ${response.status}`);
      }

      const data = await response.json();

      if (data && data.result) {
        // Process cart items to ensure they have images from allItems
        if (data.result.items && data.result.items.length > 0) {
          data.result.items = data.result.items.map((item: any) => {
            // Find matching product in allItems
            const matchingProduct = allItems.find(
              (product) => product.menu_item_id === item.menu_item_id
            );

            // If matching product found and has a valid image, use it
            if (
              matchingProduct &&
              matchingProduct.image &&
              !matchingProduct.image.includes("no_image")
            ) {
              console.log(
                `Using image from allItems for ${item.menu_item_id}:`,
                matchingProduct.image
              );
              return {
                ...item,
                image: matchingProduct.image,
              };
            }

            // If item already has a valid image, keep it
            if (item.image && !item.image.includes("no_image")) {
              console.log(
                `Item ${item.menu_item_id} already has image:`,
                item.image
              );
              return item;
            }

            // Default fallback image
            console.log(`Using default image for ${item.menu_item_id}`);
            return {
              ...item,
              image:
                "https://static.tossdown.com/images/574664e6-d1c3-408e-a13d-024aa020a669.webp",
            };
          });
        }

        // Store the raw cart data for API calls
        setRawCartData(data.result);
        // Update Redux state for UI display
        dispatch(setCartData(data.result));

        // Track if cart was empty when opened
        setWasCartEmpty(!data.result.items || data.result.items.length === 0);
      }
    } catch (error) {
      console.error("Failed to fetch cart data:", error);
    } finally {
      setIsCartLoading(false);
    }
  }, [businessId, uniqueOrderId, allItems, dispatch]);

  // Fetch cart data when the component becomes visible
  useEffect(() => {
    if (visible) {
      fetchCartData();
      // Reset loading states when cart is opened
      setLoadingItems({});
    }
  }, [visible, fetchCartData]);

  // Reset loading states when cart items change
  useEffect(() => {
    // If cart is empty or cart items change, reset loading states
    if (!cart?.items || cart.items.length === 0) {
      setLoadingItems({});
    }
  }, [cart?.items]);

  const grandTotal = cart?.gtotal;

  // Handle quantity change for cart items
  const handleQuantityChange = async (
    item: any,
    action: "add" | "sub" | "delete"
  ) => {
    // Only check business hours for adding items, not for removing or deleting
    if (action === "add") {
      // Check if business is closed
      if (checkBusinessHours(businessHours)) {
        return; // Don't proceed if business is closed
      }
    }

    if (!rawCartData) {
      console.error("Raw cart data is not available");
      return;
    }

    // Set loading state for the specific item and action
    setLoadingItems((prev) => ({
      ...prev,
      [item.menu_item_id]: {
        ...prev[item.menu_item_id],
        [action]: true,
      },
    }));

    try {
      // If we're subtracting and quantity is 1, we should delete the item
      if (action === "sub" && item.dqty === 1) {
        action = "delete";
      }

      console.log("Item being modified:", JSON.stringify(item, null, 2));
      console.log("Action:", action);

      // Parse the option_set string to an object
      let formattedOptions = {};

      if (item.option_set) {
        try {
          // Check if option_set is a string and parse it
          if (typeof item.option_set === "string") {
            formattedOptions = JSON.parse(item.option_set);
            console.log(
              "Parsed options:",
              JSON.stringify(formattedOptions, null, 2)
            );
          } else {
            // If it's already an object, use it directly
            formattedOptions = item.option_set;
          }
        } catch (error) {
          console.error("Error parsing option_set:", error);
          // If parsing fails, use an empty object
          formattedOptions = {};
        }
      }

      // Prepare the payload with the exact item data, only changing the action
      const payload = {
        action: action,
        current_date: new Date().toISOString(),
        unique_order_id: uniqueOrderId,
        order_type: orderType,
        business_id: `${businessId}`,
        branch_id: `${branchId}`,
        items: [
          {
            id: String(item.menu_item_id),
            name: item.dname,
            price: String(item.dprice),
            qty: 1, // Always 1 as the API handles the increment/decrement
            discount: item.discount || 0,
            item_level_discount_value: item.item_level_discount_value || 0,
            tax: String(item.tax || 0),
            item_level_tax_value: item.item_level_tax_value || 0,
            weight_value: String(item.weight_value) || "0",
            weight_unit: item.weight_unit || "kg",
            comment: item.comment || "",
            category_id: String(item.category_id),
            product_code: item.product_code || "0",
            category_name: item.category_name,
            brand_id: "0",
            options: formattedOptions, // Use the parsed options directly
            image: item.image,
          },
        ],
      };

      console.log("Final payload:", JSON.stringify(payload, null, 2));

      const response = await fetch(`${API_ENDPOINTS.CART}/${businessId}/cart`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Server error:", errorText);
        Alert.alert(
          "Error",
          `Server error: ${response.status}. Please try again.`
        );
        return;
      }

      const result = await response.json();
      console.log("Cart API response:", JSON.stringify(result, null, 2));

      if (result && result.result) {
        // Process the result to ensure images are attached
        if (result.result.items && result.result.items.length > 0) {
          result.result.items = result.result.items.map((item: any) => {
            // Find matching product in allItems
            const matchingProduct = allItems.find(
              (product) => product.menu_item_id === item.menu_item_id
            );

            // If matching product found and has a valid image, use it
            if (
              matchingProduct &&
              matchingProduct.image &&
              !matchingProduct.image.includes("no_image")
            ) {
              return {
                ...item,
                image: matchingProduct.image,
              };
            }

            // If item already has a valid image, keep it
            if (item.image && !item.image.includes("no_image")) {
              return item;
            }

            // Default fallback image
            return {
              ...item,
              image:
                "https://static.tossdown.com/images/574664e6-d1c3-408e-a13d-024aa020a669.webp",
            };
          });
        }

        // Update both the raw cart data and Redux state
        setRawCartData(result.result);
        dispatch(setCartData(result.result));

        // If this was a delete action and the cart is now empty, close the cart
        if (
          action === "delete" &&
          (!result.result.items || result.result.items.length === 0)
        ) {
          // Reset all loading states when cart becomes empty
          setLoadingItems({});

          // Check if cart was not empty before and is now empty
          if (!wasCartEmpty) {
            onClose(true); // Pass true to indicate cart is now empty
          } else {
            onClose();
          }
        }
      } else {
        Alert.alert("Error", result.message || "Failed to update cart item.");
      }
    } catch (error) {
      console.error("Error updating cart:", error);
      Alert.alert("Error", "Failed to update cart item");
    } finally {
      // Reset loading state for the specific item and action
      setLoadingItems((prev) => {
        // If this was a delete action, remove the entire item from loading state
        if (action === "delete") {
          const newState = { ...prev };
          delete newState[item.menu_item_id];
          return newState;
        } else {
          const updatedItem = { ...prev[item.menu_item_id] };
          if (updatedItem) {
            updatedItem[action] = false;
          }
          return {
            ...prev,
            [item.menu_item_id]: updatedItem,
          };
        }
      });
    }
  };

  // Function to show toast message
  const showToastMessage = (message: string) => {
    if (Platform.OS === "android") {
      ToastAndroid.show(message, ToastAndroid.LONG);
    } else {
      // For iOS, use Alert since ToastAndroid is not available
      Alert.alert("Notice", message);
    }
  };

  // Handle checkout button click
  const handleCheckout = () => {
    // Check if cart total meets minimum spend requirement
    const cartTotal = cart?.total || 0;
    const minSpend = parseFloat(minimumSpend);

    if (minSpend > 0 && cartTotal < minSpend) {
      // Show toast message about minimum spend requirement with currency symbol
      const currencySymbol = getCurrencySymbol(currency);
      showToastMessage(`Minimum order limit is ${currencySymbol}${minSpend}`);
      return;
    }

    onClose(); // Close the modal

    // Check if we're in the tab navigator
    const parentState = navigation.getParent()?.getState();
    const isInTabNavigator = parentState?.routeNames.includes("HomeTab");

    if (isInTabNavigator && navigation.getParent()) {
      // If we're in the tab navigator, navigate to Checkout through the parent
      navigation.getParent().navigate("Checkout");
    } else {
      // If we're not in the tab navigator, navigate directly
      navigation.navigate("Checkout");
    }
  };

  // Handle close with empty cart check
  const handleClose = () => {
    const isCartEmpty = !cart?.items || cart.items.length === 0;
    // Only show empty cart message if cart was not empty before and is now empty
    if (isCartEmpty && !wasCartEmpty) {
      onClose(true);
    } else {
      onClose();
    }
  };

  // Toggle expanded state for a cart item
  const toggleItemDetails = (itemId: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.cartContainer}>
          {/* Close Button */}
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <MaterialIcons name="close" size={24} color="#333" />
          </TouchableOpacity>

          <Text style={styles.title}>Your Cart</Text>

          {/* Loading Indicator */}
          {isCartLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#E4002B" />
            </View>
          )}

          {/* Cart Items List */}
          {!isCartLoading && (
            <>
              {cart?.items && cart.items.length > 0 ? (
                <FlatList
                  data={cart.items}
                  keyExtractor={(item) => item.menu_item_id}
                  renderItem={({ item }) => {
                    return (
                      <View style={styles.cartItem}>
                        <View style={styles.cartItemDetails}>
                          <Text style={styles.cartItemName}>{item.dname}</Text>

                          {/* View/Hide Details Button - Only show if option_set exists and has content */}
                          {item.option_set &&
                            (() => {
                              // Parse option_set if it's a string
                              let options = {};
                              try {
                                if (typeof item.option_set === "string") {
                                  options = JSON.parse(item.option_set);
                                } else {
                                  options = item.option_set;
                                }

                                // Only show the button if options has content (not empty object)
                                const hasOptions =
                                  Object.keys(options).length > 0;

                                if (hasOptions) {
                                  return (
                                    <TouchableOpacity
                                      style={styles.detailsToggleButton}
                                      onPress={() =>
                                        toggleItemDetails(item.menu_item_id)
                                      }
                                    >
                                      <Text style={styles.detailsToggleText}>
                                        {expandedItems[item.menu_item_id]
                                          ? "Hide Details"
                                          : "View Details"}
                                      </Text>
                                      <MaterialIcons
                                        name={
                                          expandedItems[item.menu_item_id]
                                            ? "keyboard-arrow-up"
                                            : "keyboard-arrow-down"
                                        }
                                        size={18}
                                        color="#666666"
                                      />
                                    </TouchableOpacity>
                                  );
                                }
                                return null;
                              } catch (error) {
                                console.error(
                                  "Error parsing option_set:",
                                  error
                                );
                                return null;
                              }
                            })()}

                          {/* Option Sets - Only show when expanded and options exist */}
                          {item.option_set &&
                            expandedItems[item.menu_item_id] && (
                              <View style={styles.optionSetsContainer}>
                                {(() => {
                                  // Parse option_set if it's a string
                                  let options = {};
                                  try {
                                    if (typeof item.option_set === "string") {
                                      options = JSON.parse(item.option_set);
                                    } else {
                                      options = item.option_set;
                                    }

                                    // Only render if options has content
                                    if (Object.keys(options).length === 0) {
                                      return null;
                                    }

                                    // Render option sets
                                    return Object.entries(options).map(
                                      ([optionSetName, optionItems], index) => (
                                        <View
                                          key={index}
                                          style={styles.optionSetRow}
                                        >
                                          <View style={styles.optionSetHeader}>
                                            <Text style={styles.optionSetName}>
                                              {optionSetName}
                                            </Text>
                                          </View>
                                          <View
                                            style={styles.optionItemsContainer}
                                          >
                                            {Array.isArray(optionItems) ? (
                                              optionItems.map((option, i) => (
                                                <View
                                                  key={i}
                                                  style={styles.optionItem}
                                                >
                                                  <View
                                                    style={styles.optionItemRow}
                                                  >
                                                    <Text
                                                      style={
                                                        styles.optionItemName
                                                      }
                                                    >
                                                      {option.name}
                                                      {option.quantity > 1
                                                        ? ` x${option.quantity}`
                                                        : ""}
                                                    </Text>
                                                    {Number(option.price) >
                                                      0 && (
                                                      <Text
                                                        style={
                                                          styles.optionItemPrice
                                                        }
                                                      >
                                                        {formatPrice(
                                                          option.price *
                                                            (option.quantity ||
                                                              1),
                                                          currency
                                                        )}
                                                      </Text>
                                                    )}
                                                  </View>

                                                  {/* Inner Option Sets (if available) */}
                                                  {option.inner_options &&
                                                    Array.isArray(
                                                      option.inner_options
                                                    ) &&
                                                    option.inner_options
                                                      .length > 0 && (
                                                      <View
                                                        style={
                                                          styles.innerOptionsContainer
                                                        }
                                                      >
                                                        {option.inner_options.map(
                                                          (
                                                            innerOption: any,
                                                            innerIndex: number
                                                          ) => (
                                                            <View
                                                              key={innerIndex}
                                                              style={
                                                                styles.innerOptionRow
                                                              }
                                                            >
                                                              <Text
                                                                style={
                                                                  styles.innerOptionName
                                                                }
                                                              >
                                                                •{" "}
                                                                {
                                                                  innerOption.name
                                                                }
                                                                {innerOption.quantity >
                                                                1
                                                                  ? ` x${innerOption.quantity}`
                                                                  : ""}
                                                              </Text>
                                                              {Number(
                                                                innerOption.price
                                                              ) > 0 && (
                                                                <Text
                                                                  style={
                                                                    styles.innerOptionPrice
                                                                  }
                                                                >
                                                                  {formatPrice(
                                                                    innerOption.price *
                                                                      (innerOption.quantity ||
                                                                        1),
                                                                    currency
                                                                  )}
                                                                </Text>
                                                              )}
                                                            </View>
                                                          )
                                                        )}
                                                      </View>
                                                    )}
                                                </View>
                                              ))
                                            ) : (
                                              <View
                                                style={styles.optionItemRow}
                                              >
                                                <Text
                                                  style={styles.optionItemName}
                                                >
                                                  {typeof optionItems ===
                                                    "object" &&
                                                  optionItems !== null &&
                                                  "name" in optionItems
                                                    ? String(optionItems.name)
                                                    : "Option"}
                                                  {typeof optionItems ===
                                                    "object" &&
                                                  optionItems !== null &&
                                                  "quantity" in optionItems &&
                                                  Number(optionItems.quantity) >
                                                    1
                                                    ? ` x${optionItems.quantity}`
                                                    : ""}
                                                </Text>
                                                {typeof optionItems ===
                                                  "object" &&
                                                  optionItems !== null &&
                                                  "price" in optionItems &&
                                                  Number(optionItems.price) >
                                                    0 && (
                                                    <Text
                                                      style={
                                                        styles.optionItemPrice
                                                      }
                                                    >
                                                      {formatPrice(
                                                        Number(
                                                          optionItems.price
                                                        ) *
                                                          (typeof optionItems ===
                                                            "object" &&
                                                          optionItems !==
                                                            null &&
                                                          "quantity" in
                                                            optionItems
                                                            ? Number(
                                                                optionItems.quantity
                                                              ) || 1
                                                            : 1),
                                                        currency
                                                      )}
                                                    </Text>
                                                  )}
                                              </View>
                                            )}
                                          </View>
                                        </View>
                                      )
                                    );
                                  } catch (error) {
                                    console.error(
                                      "Error parsing option_set:",
                                      error
                                    );
                                    return null;
                                  }
                                })()}
                              </View>
                            )}

                          {/* Price and Counter Row */}
                          <View style={styles.priceCounterRow}>
                            {/* Counter at left end */}
                            <View style={styles.counterContainer}>
                              <TouchableOpacity
                                style={styles.counterButton}
                                onPress={() =>
                                  handleQuantityChange(item, "sub")
                                }
                                disabled={
                                  !!loadingItems[item.menu_item_id]?.sub ||
                                  !!loadingItems[item.menu_item_id]?.add ||
                                  Object.values(loadingItems).some(
                                    (item) => item?.delete
                                  )
                                }
                              >
                                {loadingItems[item.menu_item_id]?.sub ? (
                                  <ActivityIndicator
                                    size="small"
                                    color="#333"
                                  />
                                ) : item.dqty === 1 ? (
                                  <MaterialIcons
                                    name="delete"
                                    size={16}
                                    color="#E4002B"
                                  />
                                ) : (
                                  <Text style={styles.counterButtonText}>
                                    -
                                  </Text>
                                )}
                              </TouchableOpacity>
                              <Text style={styles.counterText}>
                                {item.dqty}
                              </Text>
                              <TouchableOpacity
                                style={styles.counterButton}
                                onPress={() =>
                                  handleQuantityChange(item, "add")
                                }
                                disabled={
                                  !!loadingItems[item.menu_item_id]?.add ||
                                  !!loadingItems[item.menu_item_id]?.sub ||
                                  Object.values(loadingItems).some(
                                    (item) => item?.delete
                                  )
                                }
                              >
                                {loadingItems[item.menu_item_id]?.add ? (
                                  <ActivityIndicator
                                    size="small"
                                    color="#333"
                                  />
                                ) : (
                                  <Text style={styles.counterButtonText}>
                                    +
                                  </Text>
                                )}
                              </TouchableOpacity>
                            </View>

                            {/* Price at right end */}
                            <Text style={styles.cartItemPrice}>
                              {formatPrice(item.dtotal, currency)}
                            </Text>
                          </View>
                        </View>
                      </View>
                    );
                  }}
                  contentContainerStyle={styles.cartList}
                />
              ) : (
                <View style={styles.emptyCartContainer}>
                  <MaterialIcons
                    name="shopping-cart"
                    size={64}
                    color="#CCCCCC"
                  />
                  <Text style={styles.emptyCartText}>
                    Add items to view cart
                  </Text>
                </View>
              )}
            </>
          )}

          {/* Order Summary Section */}
          {cart?.items && cart.items.length > 0 && (
            <View style={styles.summaryContainer}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Subtotal</Text>
                <Text style={styles.summaryValue}>
                  {formatPrice(cart?.total || 0, currency)}
                </Text>
              </View>

              {cart?.tax_value > 0 && (
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Tax</Text>
                  <Text style={styles.summaryValue}>
                    {formatPrice(cart?.tax_value || 0, currency)}
                  </Text>
                </View>
              )}

              {cart?.discount_value > 0 && (
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Discount</Text>
                  <Text style={styles.summaryValue}>
                    -{formatPrice(cart?.discount_value || 0, currency)}
                  </Text>
                </View>
              )}

              {/* Minimum Spend Warning */}
              {parseFloat(minimumSpend) > 0 &&
                (cart?.total || 0) < parseFloat(minimumSpend) && (
                  <View style={styles.minimumSpendWarning}>
                    <View style={styles.minimumSpendContent}>
                      <MaterialIcons
                        name="info-outline"
                        size={16}
                        color="#E4002B"
                      />
                      <Text style={styles.minimumSpendText}>
                        Minimum order amount is{" "}
                        {formatPrice(minimumSpend, currency)}
                      </Text>
                    </View>
                  </View>
                )}
            </View>
          )}

          {/* Proceed to Checkout Button */}
          {cart?.items && cart.items.length > 0 && (
            <TouchableOpacity
              style={[
                styles.checkoutButton,
                parseFloat(minimumSpend) > 0 &&
                  (cart?.total || 0) < parseFloat(minimumSpend) &&
                  styles.checkoutButtonDisabled,
              ]}
              onPress={handleCheckout}
              disabled={
                isCartLoading ||
                Object.values(loadingItems).some(
                  (item) => !!item?.add || !!item?.sub || !!item?.delete
                ) ||
                (parseFloat(minimumSpend) > 0 &&
                  (cart?.total || 0) < parseFloat(minimumSpend))
              }
            >
              <View style={styles.checkoutRow}>
                <Text style={styles.checkoutTotalPrice}>
                  {formatPrice(grandTotal, currency)}
                </Text>
                {isCartLoading ||
                Object.values(loadingItems).some(
                  (item) => !!item?.add || !!item?.sub || !!item?.delete
                ) ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.checkoutButtonText}>
                    Proceed to Checkout
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  cartContainer: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: height * 0.9, // Use 90% of screen height
    maxHeight: height - 50, // Maximum height with some space at top
    padding: 20,
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    top: 20,
    right: 20,
    zIndex: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    marginTop: 10,
  },
  cartList: {
    flexGrow: 1,
  },
  cartItem: {
    flexDirection: "row",
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    paddingBottom: 15,
  },
  cartItemDetails: {
    flex: 1,
    justifyContent: "space-between",
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  priceCounterRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 5,
  },
  cartItemPrice: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#000000",
  },
  counterContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  counterButton: {
    width: 30,
    height: 30,
    backgroundColor: "#f0f0f0",
    borderRadius: 15,
    justifyContent: "center",
    alignItems: "center",
  },
  counterButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  counterText: {
    fontSize: 16,
    fontWeight: "bold",
    marginHorizontal: 15,
  },
  // Summary section styles
  summaryContainer: {
    marginBottom: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: "#666666",
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#000000",
  },
  checkoutButton: {
    backgroundColor: "#000000", // Changed from #E4002B
    padding: 15,
    borderRadius: 80,
    marginTop: 0,
  },
  checkoutButtonDisabled: {
    backgroundColor: "#666666", // Lighter color for disabled state
    opacity: 0.7,
  },
  checkoutRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  checkoutTotalPrice: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  checkoutButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyCartText: {
    fontSize: 18,
    color: "#666666",
    marginTop: 16,
    fontWeight: "500",
  },
  minimumSpendWarning: {
    backgroundColor: "#FFEEEE",
    padding: 10,
    borderRadius: 80,
    marginTop: 10,
  },
  minimumSpendContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  minimumSpendText: {
    color: "#E4002B",
    fontSize: 14,
    marginLeft: 8,
  },
  // Details toggle button
  detailsToggleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginBottom: 8,
    paddingVertical: 4,
  },
  detailsToggleText: {
    fontSize: 13,
    color: "#666666",
    fontWeight: "500",
    marginRight: 4,
  },
  // Option set styles
  optionSetsContainer: {
    marginTop: 5,
    marginBottom: 10,
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
    padding: 10,
  },
  optionSetRow: {
    marginBottom: 0,
  },
  optionSetHeader: {
    borderBottomWidth: 1,
    borderBottomColor: "#eeeeee",
    paddingBottom: 6,
    marginBottom: 8,
  },
  optionSetName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333333",
  },
  optionItemsContainer: {
    marginLeft: 4,
  },
  optionItem: {
    marginBottom: 4,
  },
  optionItemRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  optionItemName: {
    fontSize: 13,
    color: "#444444",
    flex: 1,
  },
  optionItemPrice: {
    fontSize: 13,
    color: "#666666",
    fontWeight: "500",
  },
  innerOptionsContainer: {
    marginLeft: 12,
    marginTop: 4,
  },
  innerOptionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 2,
  },
  innerOptionName: {
    fontSize: 12,
    color: "#666666",
    flex: 1,
  },
  innerOptionPrice: {
    fontSize: 12,
    color: "#888888",
  },
});

export default CartScreen;
