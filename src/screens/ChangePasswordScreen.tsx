"use client";
import { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  ToastAndroid,
  ScrollView,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { BRAND_ID, API_ENDPOINTS } from "../config";

const ChangePasswordScreen = () => {
  const navigation = useNavigation();
  const { logo } = useSelector((state) => state.order);
  const { userData } = useSelector((state) => state.auth);

  // State
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("error"); // success or error

  // Set header options
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: "Change Password",
      headerStyle: {
        backgroundColor: "#FFFFFF",
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: "#000000",
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginLeft: 16 }}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  // Show toast message
  const showToastMessage = (message, type = "error") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);

    if (Platform.OS === "android") {
      ToastAndroid.show(message, ToastAndroid.LONG);
    }
  };

  // Validate form
  const validateForm = () => {
    // Clear previous error
    setErrorMessage("");

    if (!oldPassword.trim()) {
      setErrorMessage("Please enter your current password");
      return false;
    }

    if (!newPassword.trim()) {
      setErrorMessage("Please enter a new password");
      return false;
    }

    if (newPassword.length < 6) {
      setErrorMessage("New password must be at least 6 characters long");
      return false;
    }

    if (oldPassword === newPassword) {
      setErrorMessage(
        "New password must be different from the current password"
      );
      return false;
    }

    return true;
  };

  // Handle change password
  const handleChangePassword = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formData = new FormData();
      formData.append("email", userData?.user_email || "");
      formData.append("password", oldPassword); // Old password
      formData.append("new_password", newPassword);
      formData.append("brand_id", BRAND_ID);

      // Make API call to change password
      const response = await fetch(API_ENDPOINTS.CHANGE_PASSWORD, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const data = await response.json();
      console.log("Change password response:", data);

      if (data.status === "1") {
        // Show success message from API
        showToastMessage(
          data.message || "Password changed successfully",
          "success"
        );

        // Clear form
        setOldPassword("");
        setNewPassword("");

        // Navigate back after a short delay
        setTimeout(() => {
          navigation.goBack();
        }, 1500);
      } else {
        // Show error message from API
        showToastMessage(
          data.message || "Failed to change password. Please try again."
        );
      }
    } catch (error) {
      console.error("Error changing password:", error);
      showToastMessage("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Logo */}
          <View style={styles.logoContainer}>
            {logo ? (
              <Image
                source={{ uri: logo }}
                style={styles.logo}
                resizeMode="contain"
              />
            ) : (
              <Image
                source={require("../assets/splash.jpg")}
                style={styles.logo}
                resizeMode="contain"
              />
            )}
          </View>

          {/* Form */}
          <View style={styles.formContainer}>
            {/* Old Password */}
            <Text style={styles.inputLabel}>Old Password</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Enter Old Password"
                value={oldPassword}
                onChangeText={setOldPassword}
                secureTextEntry={!showOldPassword}
                placeholderTextColor="#999999"
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowOldPassword(!showOldPassword)}
              >
                <MaterialIcons
                  name={showOldPassword ? "visibility" : "visibility-off"}
                  size={24}
                  color="#777777"
                />
              </TouchableOpacity>
            </View>

            {/* New Password */}
            <Text style={styles.inputLabel}>New Password</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Enter New Password"
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry={!showNewPassword}
                placeholderTextColor="#999999"
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowNewPassword(!showNewPassword)}
              >
                <MaterialIcons
                  name={showNewPassword ? "visibility" : "visibility-off"}
                  size={24}
                  color="#777777"
                />
              </TouchableOpacity>
            </View>

            {/* Error Message */}
            {errorMessage ? (
              <Text style={styles.errorText}>{errorMessage}</Text>
            ) : null}

            {/* Submit Button - Updated to match Login button UI */}
            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleChangePassword}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.submitButtonText}>Change Password</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Add extra space at the bottom for better scrolling */}
          <View style={{ height: 40 }} />
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === "success" ? styles.successToast : styles.errorToast,
          ]}
        >
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    padding: 24,
  },
  logoContainer: {
    alignItems: "center",
    marginVertical: 32,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  formContainer: {
    width: "100%",
  },
  inputLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 12,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 12,
    marginBottom: 24,
    paddingHorizontal: 16,
    height: 56,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#000000",
  },
  eyeIcon: {
    padding: 8,
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginBottom: 16,
    marginLeft: 4,
  },
  submitButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    height: 56,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  toast: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 0.9)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 59, 48, 0.9)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: "flex-start",
    paddingBottom: 20,
  },
});

export default ChangePasswordScreen;
