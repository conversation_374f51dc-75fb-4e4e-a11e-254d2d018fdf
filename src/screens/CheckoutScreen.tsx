"use client";

import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  ToastAndroid,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import {
  resetCartData,
  resetUniqueOrderId,
  setCartData,
} from "../store/orderSlice/index";
import { loginSuccess } from "../store/authSlice";
import { MaterialIcons } from "@expo/vector-icons";
import axios from "axios";
import moment from "moment";

// Custom hooks for API calls
import { useGetBusinessDetails } from "../hooks/useGetBusinessDetails";
import { useCreateOrder } from "../hooks/useCreateOrder";
import { useCartCalculation } from "../hooks/useCartCalculation";
import { useGetPickupHours } from "../hooks/useGetPickupHours";

// Components
import CartSummary from "../components/checkout/CartSummary";
import AddressSelectionModal from "../components/checkout/AddressSelectionModal";
import OrderConfirmationModal from "../components/checkout/OrderConfirmationModal";
import OrderScheduleModal from "../components/checkout/OrderScheduleModal";

// Utils
import {
  getCartCalculationPayload,
  createOrderPayload,
  setCartCalculationResponse,
  getCart,
} from "../utils/apiConfig";
import { API_ENDPOINTS, BUSINESS_ID } from "../config";

const CheckoutScreen = () => {
  // State for form fields
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [additionalNote, setAdditionalNote] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [processingOrder, setProcessingOrder] = useState(false);
  const [calculatedCartData, setCalculatedCartData] = useState(null);
  const [originalCartData, setOriginalCartData] = useState(null);
  const [businessDetails, setBusinessDetails] = useState(null);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  const [orderId, setOrderId] = useState("");
  const [pickupSchedule, setPickupSchedule] = useState<{
    date: string;
    time: string;
  } | null>(null);
  const [showPickupScheduleModal, setShowPickupScheduleModal] = useState(false);

  // Add these new state variables at the top of the component with the other state declarations
  const [pickupSlots, setPickupSlots] = useState<string[]>([]);
  const [pickupSlotsLoading, setPickupSlotsLoading] = useState(false);

  // Validation states
  const [fullNameError, setFullNameError] = useState("");
  const [emailError, setEmailError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [addressError, setAddressError] = useState("");
  const [pickupScheduleError, setPickupScheduleError] = useState("");

  // Toast message state
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("error"); // success or error

  // Local state for cart summary values
  const [localDeliveryCharges, setLocalDeliveryCharges] = useState(0);
  const [localDiscount, setLocalDiscount] = useState(0);
  const [localCouponDiscount, setLocalCouponDiscount] = useState(0);
  const [localTip, setLocalTip] = useState(0);
  const [localSubtotal, setLocalSubtotal] = useState(0);
  const [localTax, setLocalTax] = useState(0);
  const [localTotal, setLocalTotal] = useState(0);

  const [userCity, setUserCity] = useState("");
  const [userArea, setUserArea] = useState("");
  const [userCountry, setUserCountry] = useState("");
  const [userPostalCode, setUserPostalCode] = useState("");
  const [userLocation, setUserLocation] = useState({ lat: "0", lng: "0" });

  const navigation = useNavigation();
  const dispatch = useDispatch();

  // Redux state
  const orderType = useSelector((state) => state.order.orderType);
  const branchId = useSelector(
    (state) => state.order.selectedBranch?.branch_id
  );
  const uniqueOrderId = useSelector((state) => state.order.uniqueOrderId);
  const businessId = useSelector((state) => state.order.businessId);
  const cartData = useSelector((state) => state.order.cart);
  const currency = useSelector((state) => state.order.currency || "Rs");
  const authData = useSelector((state) => state.auth);

  // Custom hooks
  const { getBusinessDetailsAPICall, loading: businessLoading } =
    useGetBusinessDetails();
  const { createOrderAPICall, loading: createOrderLoading } = useCreateOrder();
  const { getCartCalculationsAPICall, getCartCalculationsLoading } =
    useCartCalculation();
  const { getPickupHoursAPICall } = useGetPickupHours();

  // Prepare cart items for CartSummary component
  const cartItems =
    cartData?.items?.map((item) => ({
      id: item.menu_item_id || item.id || String(Math.random()),
      name: item.dname || item.name || "Item",
      price: Number.parseFloat(item.dtotal || item.dtotal) || 0,
      quantity: Number.parseInt(item.dqty || item.quantity) || 1,
    })) || [];

  // Update local cart summary values when cartData changes
  useEffect(() => {
    if (cartData) {
      console.log(
        "Cart data updated in Redux, updating local state:",
        cartData
      );

      // Update local state with values from cartData
      setLocalSubtotal(
        Number.parseFloat(cartData.total || cartData.subtotal || 0)
      );
      setLocalTax(Number.parseFloat(cartData.tax_value || 0));
      setLocalTotal(
        Number.parseFloat(cartData.gtotal || cartData.grandTotal || 0)
      );
      setLocalDeliveryCharges(
        Number.parseFloat(cartData.delivery_charges || 0)
      );
      setLocalDiscount(Number.parseFloat(cartData.discount_value || 0));
      setLocalCouponDiscount(
        Number.parseFloat(cartData.coupon_discount_value || 0)
      );
      setLocalTip(Number.parseFloat(cartData.tip || 0));
    }
  }, [cartData]);

  // Set user data from auth if available
  useEffect(() => {
    if (authData && authData.isLoggedIn && authData.userData) {
      // Extract user data from the auth state
      const userData = authData.userData;

      // Set user details in the form
      setFullName(userData.user_fullname || "");
      setEmail(userData.user_email || "");
      setPhone(
        userData.user_cphone ? userData.user_cphone.replace("+92", "") : ""
      );
      setAddress(userData.user_address || "");

      // Set location data if available
      if (userData.user_city) setUserCity(userData.user_city);
      if (userData.area) setUserArea(userData.area);
      if (userData.country) setUserCountry(userData.country);
      if (userData.postalCode) setUserPostalCode(userData.postalCode);
      if (userData.location) setUserLocation(userData.location);

      console.log("User data loaded from auth state:", userData);
    }
  }, [authData]);

  // Set header styling when component mounts
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: "Checkout",
      headerStyle: {
        backgroundColor: "#FFFFFF",
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: "#000000",
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginLeft: 16 }}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  // Initialize checkout and validate cart
  useEffect(() => {
    const initializeCheckout = async () => {
      try {
        setIsLoading(true);

        // Get business details
        const businessData = await getBusinessDetailsAPICall();

        if (businessData) {
          setBusinessDetails(businessData);
          console.log("Business details loaded:", businessData);
        } else {
          console.log("Failed to load business details");
        }

        // Fetch original cart data from API
        await fetchOriginalCartData();

        // Debug cart data
        console.log("Cart Data from Redux:", cartData);

        // Validate cart
        if (!cartData || !cartData.items || cartData.items.length === 0) {
          console.log("Cart is empty or invalid");
          Alert.alert(
            "Error",
            "Your cart is empty. Please add items to your cart."
          );
          navigation.goBack();
          return;
        }

        // Initialize local state with cart data
        setLocalSubtotal(
          Number.parseFloat(cartData.total || cartData.subtotal || 0)
        );
        setLocalTax(Number.parseFloat(cartData.tax_value || 0));
        setLocalTotal(
          Number.parseFloat(cartData.gtotal || cartData.grandTotal || 0)
        );

        setIsLoading(false);
      } catch (error) {
        console.error("Error initializing checkout:", error);
        setIsLoading(false);
        Alert.alert("Error", "Failed to load checkout data. Please try again.");
      }
    };

    initializeCheckout();
  }, []);

  // Fetch original cart data from API
  const fetchOriginalCartData = async () => {
    try {
      if (!businessId || !uniqueOrderId) {
        console.error("Missing businessId or uniqueOrderId for cart fetch");
        return;
      }

      const config = getCart(businessId, uniqueOrderId);
      const response = await axios.get(config.url);

      if (
        response.data &&
        response.data.status === 200 &&
        response.data.result
      ) {
        console.log("Original cart data fetched:", response.data.result);
        setOriginalCartData(response.data.result);
        return response.data.result;
      } else {
        console.error("Cart API error:", response.data);
        return null;
      }
    } catch (error) {
      console.error("Error fetching original cart data:", error);
      return null;
    }
  };

  // Update cart with calculated data
  useEffect(() => {
    if (calculatedCartData) {
      console.log("calculatedCartData updated:", calculatedCartData);

      // Update local state for cart summary with calculated values
      setLocalDeliveryCharges(Number(calculatedCartData.delivery_charges) || 0);
      setLocalDiscount(Number(calculatedCartData.discount_value) || 0);
      setLocalCouponDiscount(
        Number(calculatedCartData.coupon_discount_value) || 0
      );
      setLocalTip(Number(calculatedCartData.tip) || 0);
      setLocalSubtotal(Number(calculatedCartData.total) || 0);
      setLocalTax(Number(calculatedCartData.tax_value) || 0);
      setLocalTotal(Number(calculatedCartData.grand_total) || 0);
    }
  }, [calculatedCartData]);

  // Handle pickup schedule change
  const handlePickupScheduleChange = (schedule) => {
    setPickupSchedule(schedule);
    // Recalculate cart with new pickup time
    calculateCart();
  };

  // Calculate cart totals using the CartCalculation API
  const calculateCart = async () => {
    try {
      // Ensure we have items
      if (!cartData || !cartData.items || !cartData.items.length) {
        console.error("No cart items available for calculation");
        return null;
      }

      // Create a cart data object with all required fields
      const cartDataForCalculation = {
        ...cartData,
        businessId,
        branchId,
        name: fullName || "",
        email: email || "",
        phone: phone || "",
        userLocation: userLocation,
        userCountry: userCountry || "",
        userCity: userCity || "",
        userArea: userArea || "",
        userPostalCode: userPostalCode || "",
      };

      // Prepare payload for cart calculation using the existing function
      const payload = getCartCalculationPayload(
        cartDataForCalculation,
        "0", // Cash on delivery
        orderType,
        "", // No coupon code
        0 // No tip
      );

      // Log the payload for debugging
      console.log(
        "Cart Calculation Payload:",
        JSON.stringify(payload, null, 2)
      );

      // Call cart calculation API
      const result = await getCartCalculationsAPICall(payload, cartData);

      if (result && result.success) {
        console.log("Cart calculation successful:", result.data);
        setCalculatedCartData(result.data);

        // Update local state with calculated values
        setLocalDeliveryCharges(Number(result.data.delivery_charges) || 0);
        setLocalDiscount(Number(result.data.discount_value) || 0);
        setLocalCouponDiscount(Number(result.data.coupon_discount_value) || 0);
        setLocalTip(Number(result.data.tip) || 0);
        setLocalSubtotal(Number(result.data.total) || 0);
        setLocalTax(Number(result.data.tax_value) || 0);
        setLocalTotal(Number(result.data.grand_total) || 0);

        // Update Redux store with calculated cart data
        const updatedCart = setCartCalculationResponse(cartData, result.data);
        dispatch(setCartData(updatedCart));

        return result.data;
      } else {
        console.error(
          "Cart calculation API error:",
          result?.error || "Unknown error"
        );
        return null;
      }
    } catch (error) {
      console.error("Error calculating cart:", error);
      return null;
    }
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError("Email is required");
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    } else {
      setEmailError("");
      return true;
    }
  };

  // Validate phone number
  const validatePhone = (phone: string) => {
    // Allow only digits
    const phoneRegex = /^\d+$/;
    if (!phone) {
      setPhoneError("Phone number is required");
      return false;
    } else if (!phoneRegex.test(phone)) {
      setPhoneError("Phone number should contain only digits");
      return false;
    } else if (phone.length < 10 || phone.length > 11) {
      setPhoneError("Phone number should be 10-11 digits");
      return false;
    } else {
      setPhoneError("");
      return true;
    }
  };

  // Handle phone number input - allow only digits
  const handlePhoneChange = (text: string) => {
    // Remove any non-digit characters
    const formattedPhone = text.replace(/[^0-9]/g, "");
    setPhone(formattedPhone);
    validatePhone(formattedPhone);
  };

  // Handle email input
  const handleEmailChange = (text: string) => {
    setEmail(text);
    validateEmail(text);
  };

  // Function to get formatted pickup location
  const getPickupLocation = () => {
    if (businessDetails) {
      const { address, location, city, country } =
        businessDetails.branchDetails;
      return [address, location, city, country].filter(Boolean).join(", ");
    }
    return "Loading pickup location...";
  };

  // Function to sign up a user with the provided data
  const signupUser = async (name, email, phone) => {
    try {
      console.log("Signing up user with data:", { name, email, phone });

      // Generate a random password (8 characters)
      const generateRandomPassword = () => {
        const chars =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
      };

      const password = generateRandomPassword();

      // Create the payload according to the required structure
      const payload = {
        name: name,
        email: email,
        phone: phone,
        gender: "",
        password: password,
        dob: "",
        city: userCity || "",
        address: address || "",
        token: "",
        device: Platform.OS, // "ios" or "android"
        source: "app",
        facebook_id: "",
        google_id: "",
        apple_id: "",
      };

      console.log("Signup payload:", JSON.stringify(payload, null, 2));

      const response = await fetch(API_ENDPOINTS.SIGNUP, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (data.status === 200) {
        console.log("Signup successful:", data.result);

        // Extract user data from response
        const userData = {
          user_id: data.result.user_id,
          user_email: data.result.user_email,
          user_fullname: data.result.user_fullname,
          user_cphone: data.result.user_cphone,
          td_user_id: data.result.td_user_id,
          token: data.result.token,
          refresh_token: data.result.refresh_token,
          user_gender: data.result.user_gender || "",
          user_address: data.result.user_address || "",
          user_fbid: data.result.user_fbid || "",
          app_secret: data.result.app_secret || "",
          user_dob: data.result.user_dob || "",
          user_city: data.result.user_city || "",
          payment_settings: data.result.payment_settings || null,
        };

        // Update Redux store with user data
        dispatch(loginSuccess(userData));

        return {
          success: true,
          userData: userData,
        };
      } else {
        console.error("Signup failed:", data.message);
        return {
          success: false,
          error: data.message || "Signup failed",
        };
      }
    } catch (error) {
      console.error("Error during signup:", error);
      return {
        success: false,
        error: error.message || "Network error during signup",
      };
    }
  };

  // Show toast message
  const showToastMessage = (message, type = "error") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);

    // Also show native toast on Android for better UX
    if (Platform.OS === "android") {
      ToastAndroid.show(message, ToastAndroid.LONG);
    }
  };

  // Reset all field errors
  const resetFieldErrors = () => {
    setFullNameError("");
    setEmailError("");
    setPhoneError("");
    setAddressError("");
    setPickupScheduleError("");
  };

  // Handle order submission
  const handlePlaceOrder = async () => {
    console.log("Placing order...");

    // Reset all field errors first
    resetFieldErrors();

    // Check all required fields and show toast for the first missing field

    // Validate full name
    if (!fullName) {
      showToastMessage("Please enter your full name");
      setFullNameError("Full name is required");
      return;
    }

    // Validate email
    if (!email) {
      showToastMessage("Please enter your email address");
      setEmailError("Email is required");
      return;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      showToastMessage("Please enter a valid email address");
      setEmailError("Invalid email format");
      return;
    }

    // Validate phone
    if (!phone) {
      showToastMessage("Please enter your phone number");
      setPhoneError("Phone number is required");
      return;
    } else if (!/^\d+$/.test(phone)) {
      showToastMessage("Phone number should contain only digits");
      setPhoneError("Phone number should contain only digits");
      return;
    } else if (phone.length < 10 || phone.length > 11) {
      showToastMessage("Phone number should be 10-11 digits");
      setPhoneError("Phone number should be 10-11 digits");
      return;
    }

    // Validate address for delivery orders
    if (orderType === "delivery" && !address) {
      showToastMessage("Please enter your address");
      setAddressError("Address is required");
      return;
    }

    // Validate pickup schedule for pickup orders
    if (
      orderType === "pickup" &&
      (!pickupSchedule || !pickupSchedule.date || !pickupSchedule.time)
    ) {
      showToastMessage("Please select a pickup time");
      setPickupScheduleError("Pickup time is required");
      return;
    }

    try {
      setProcessingOrder(true);

      // Check if user is logged in
      const isLoggedIn = authData?.isLoggedIn;
      let userId = authData?.userData?.user_id || "0";

      // If user is not logged in, sign them up first
      if (!isLoggedIn) {
        console.log(
          "User is not logged in. Creating account with checkout data..."
        );
        const signupResult = await signupUser(fullName, email, phone);

        if (signupResult.success) {
          console.log("User signed up successfully during checkout");
          userId = signupResult.userData.user_id;
        } else {
          console.log(
            "Failed to sign up user during checkout:",
            signupResult.error
          );
          // Continue with the order even if signup fails
          // The order will be created as a guest order
        }
      } else {
        console.log("User is already logged in. Using existing user data.");
      }

      // Calculate cart before placing order
      const calculatedData = await calculateCart();

      if (!calculatedData) {
        setProcessingOrder(false);
        Alert.alert(
          "Error",
          "Failed to calculate order totals. Please try again."
        );
        return;
      }

      // If we don't have original cart data, try to fetch it again
      if (!originalCartData) {
        await fetchOriginalCartData();
      }

      // Merge data from original cart and calculated cart
      const mergedCartData = {
        ...calculatedData,
        temp_order_id: originalCartData?.temp_order_id || "",
        orderid: originalCartData?.orderid || "0",
        businessId: businessId,
        branchId: branchId,
        userId: userId,
        subtotal: calculatedData.total || 0,
        grandTotal: calculatedData.grand_total || 0,
        items: calculatedData.items.map((item, index) => {
          // Find matching item in original cart data by menu_item_id
          const originalItem = originalCartData?.items?.find(
            (origItem) => origItem.menu_item_id === item.menu_item_id
          );

          return {
            ...item,
            odetailid: originalItem?.odetailid || 0,
            orderid: originalItem?.orderid || 0,
          };
        }),
      };

      console.log("Merged cart data:", JSON.stringify(mergedCartData, null, 2));

      // Prepare order details with appropriate address data based on order type
      const orderDetails = {
        name: fullName,
        email,
        phone: phone,
        notes: additionalNote,
        orderType,
        paymentMethod: "0", // Cash on delivery
        userId: userId,
        postalCode: userPostalCode,
      };

      // For delivery orders, use the customer's address
      if (orderType === "delivery") {
        orderDetails.city = userCity;
        orderDetails.area = userArea;
        orderDetails.address = address;
        orderDetails.userLocation = userLocation || { lat: "0", lng: "0" };
      }
      // For pickup orders, use the business location
      else if (orderType === "pickup" && businessDetails) {
        // Use the pickup location as address
        orderDetails.address = getPickupLocation();

        // Use the location from businessData as area
        orderDetails.area = businessDetails.branchDetails.location || "";

        // Use the city from businessData as city
        orderDetails.city = businessDetails.branchDetails.city || "";

        // Add pickup schedule
        if (pickupSchedule) {
          orderDetails.pickupDate = pickupSchedule.date;
          orderDetails.pickupTime = pickupSchedule.time;
        }
      }

      // Prepare order payload
      const orderPayload = createOrderPayload(mergedCartData, orderDetails);

      console.log("Order payload:", JSON.stringify(orderPayload, null, 2));

      // Submit order
      const createdOrderId = await createOrderAPICall(orderPayload);

      if (createdOrderId) {
        console.log("Order created successfully with ID:", createdOrderId);

        // Reset cart
        dispatch(resetUniqueOrderId());
        dispatch(resetCartData());

        setProcessingOrder(false);

        // Store the order ID and show the confirmation modal
        setOrderId(createdOrderId);
        setShowOrderConfirmation(true);
      } else {
        setProcessingOrder(false);
        Alert.alert("Error", "Failed to place order. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting order:", error);
      setProcessingOrder(false);
      Alert.alert("Error", "Failed to submit order. Please try again.");
    }
  };

  // Handle address selection
  const handleAddressSelection = useCallback(
    async (addressData, calculatedCart = null) => {
      console.log("handleAddressSelection called with:", {
        addressData,
        calculatedCart,
      });

      // Format the address for display
      const formattedAddress = [
        addressData.streetAddress,
        addressData.area,
        addressData.city,
      ]
        .filter(Boolean)
        .join(", ");
      setAddress(formattedAddress);

      // Update location data
      if (addressData.city) setUserCity(addressData.city);
      if (addressData.area) setUserArea(addressData.area);
      if (addressData.country) setUserCountry(addressData.country);
      if (addressData.postalCode) setUserPostalCode(addressData.postalCode);
      if (addressData.location) setUserLocation(addressData.location);

      // If calculated cart data is provided from the modal
      if (calculatedCart) {
        console.log(
          "Received calculated cart data in handleAddressSelection:",
          calculatedCart
        );

        // Update local state first
        setCalculatedCartData(calculatedCart);

        // Update local cart summary values immediately
        setLocalDeliveryCharges(Number(calculatedCart.delivery_charges) || 0);
        setLocalDiscount(Number(calculatedCart.discount_value) || 0);
        setLocalCouponDiscount(
          Number(calculatedCart.coupon_discount_value) || 0
        );
        setLocalTip(Number(calculatedCart.tip) || 0);
        setLocalSubtotal(Number(calculatedCart.total) || 0);
        setLocalTax(Number(calculatedCart.tax_value) || 0);
        setLocalTotal(Number(calculatedCart.grand_total) || 0);

        // Create updated cart object with calculated values
        const updatedCart = setCartCalculationResponse(
          cartData,
          calculatedCart
        );
        console.log("Dispatching updated cart to Redux:", updatedCart);

        // Update Redux store
        dispatch(setCartData(updatedCart));
      }
      // If no calculated cart data is provided, call the API to get it
      else {
        console.log("No calculated cart data provided, calculating...");
        await calculateCart();
      }
    },
    [cartData, dispatch]
  );

  // Add this code after the handleAddressSelection function
  // Handle pickup schedule selection
  const handlePickupScheduleSelection = (schedule) => {
    console.log("Selected pickup schedule:", schedule);
    setPickupSchedule(schedule);

    // Recalculate cart with new pickup time if needed
    calculateCart();
  };

  // Handle order confirmation modal close
  const handleOrderConfirmationClose = () => {
    setShowOrderConfirmation(false);
    // Reset the entire navigation stack and make Home the only screen
    navigation.reset({
      index: 0,
      routes: [{ name: "Home" }],
    });
  };

  // Add this code at the end of the component, before the return statement
  // Add the OrderScheduleModal component
  useEffect(() => {
    // If pickup is selected and we have business details, initialize pickup schedule
    if (orderType === "pickup" && businessDetails && !pickupSchedule) {
      // Set default date to today
      const today = moment().format("YYYY-MM-DD");

      // Call API to get pickup slots for today
      const fetchInitialPickupSlots = async () => {
        try {
          setPickupSlotsLoading(true);
          const slots = await getPickupHoursAPICall({
            bid: branchId,
            date: today,
            type: "pickup",
          });

          if (slots && slots.length > 0) {
            setPickupSchedule({
              date: today,
              time: slots[0],
            });
            setPickupSlots(slots);
          }
        } catch (error) {
          console.error("Error fetching initial pickup slots:", error);
        } finally {
          setPickupSlotsLoading(false);
        }
      };

      fetchInitialPickupSlots();
    }
  }, [
    orderType,
    businessDetails,
    getPickupHoursAPICall,
    branchId,
    pickupSchedule,
  ]);

  // Add this useEffect to fetch pickup slots when the pickup schedule modal is opened
  useEffect(() => {
    const fetchPickupSlots = async () => {
      if (showPickupScheduleModal && pickupSchedule?.date) {
        try {
          setPickupSlotsLoading(true);
          const slots = await getPickupHoursAPICall({
            bid: branchId,
            date: pickupSchedule.date,
            type: "pickup",
          });
          setPickupSlots(slots || []);
        } catch (error) {
          console.error("Error fetching pickup slots:", error);
          setPickupSlots([]);
        } finally {
          setPickupSlotsLoading(false);
        }
      }
    };

    fetchPickupSlots();
  }, [
    showPickupScheduleModal,
    pickupSchedule?.date,
    getPickupHoursAPICall,
    branchId,
  ]);

  // Show loading indicator while fetching data
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
        <ActivityIndicator size="large" color="#000000" />
        <Text style={styles.loadingText}>Loading checkout...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView style={styles.scrollView}>
          {/* Delivery Details or Pickup Details based on order type */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>
              {orderType === "delivery" ? "Delivery details" : "Pickup details"}
            </Text>

            {/* Full Name */}
            <Text style={styles.inputLabel}>Full name*</Text>
            <TextInput
              style={[
                styles.input,
                fullNameError ? styles.inputError : null,
                authData?.isLoggedIn ? styles.inputDisabled : null,
              ]}
              placeholder="Enter Your Name"
              value={fullName}
              onChangeText={(text) => {
                setFullName(text);
                if (fullNameError) setFullNameError("");
              }}
              editable={!authData?.isLoggedIn}
            />
            {fullNameError ? (
              <Text style={styles.errorText}>{fullNameError}</Text>
            ) : null}

            {/* Email */}
            <Text style={styles.inputLabel}>Email address*</Text>
            <TextInput
              style={[
                styles.input,
                emailError ? styles.inputError : null,
                authData?.isLoggedIn ? styles.inputDisabled : null,
              ]}
              placeholder="Enter email address"
              keyboardType="email-address"
              value={email}
              onChangeText={handleEmailChange}
              onBlur={() => validateEmail(email)}
              editable={!authData?.isLoggedIn}
            />
            {emailError ? (
              <Text style={styles.errorText}>{emailError}</Text>
            ) : null}

            {/* Phone */}
            <Text style={styles.inputLabel}>Phone number*</Text>
            <TextInput
              style={[
                styles.input,
                phoneError ? styles.inputError : null,
                authData?.isLoggedIn ? styles.inputDisabled : null,
              ]}
              placeholder="Phone number"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={handlePhoneChange}
              onBlur={() => validatePhone(phone)}
              maxLength={11}
              editable={!authData?.isLoggedIn}
            />
            {phoneError ? (
              <Text style={styles.errorText}>{phoneError}</Text>
            ) : null}

            {orderType === "delivery" ? (
              <>
                {/* Address */}
                <Text style={styles.inputLabel}>Your address*</Text>
                <TouchableOpacity
                  style={[
                    styles.input,
                    styles.addressInput,
                    addressError ? styles.inputError : null,
                  ]}
                  onPress={() => {
                    setShowAddressModal(true);
                    if (addressError) setAddressError("");
                  }}
                >
                  <Text
                    style={
                      address ? styles.addressText : styles.addressPlaceholder
                    }
                  >
                    {address || "Enter your address"}
                  </Text>
                  <MaterialIcons name="location-on" size={20} color="#666" />
                </TouchableOpacity>
                {addressError ? (
                  <Text style={styles.errorText}>{addressError}</Text>
                ) : null}
              </>
            ) : (
              <>
                {/* Pickup Location */}
                <Text style={styles.inputLabel}>Pickup location*</Text>
                <View style={styles.pickupLocationContainer}>
                  <Text style={styles.pickupLocationText}>
                    {getPickupLocation()}
                  </Text>
                </View>

                {/* Schedule Pickup */}
                <Text style={styles.inputLabel}>Schedule pickup*</Text>
                <View
                  style={[
                    styles.schedulePickupContainer,
                    pickupScheduleError ? styles.inputError : null,
                  ]}
                >
                  <Text style={styles.schedulePickupText}>
                    {pickupSchedule?.date && pickupSchedule?.time
                      ? `${moment(pickupSchedule.date).format(
                          "ddd, MMM D"
                        )} - ${pickupSchedule.time}`
                      : "Select pickup time"}
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      setShowPickupScheduleModal(true);
                      if (pickupScheduleError) setPickupScheduleError("");
                    }}
                  >
                    <Text style={styles.editButton}>Edit</Text>
                  </TouchableOpacity>
                </View>
                {pickupScheduleError ? (
                  <Text style={styles.errorText}>{pickupScheduleError}</Text>
                ) : null}
              </>
            )}

            {/* Additional Note */}
            <Text style={styles.inputLabel}>Additional note</Text>
            <TextInput
              style={styles.input}
              placeholder={
                orderType === "delivery"
                  ? "Add delivery instructions"
                  : "Add pickup instructions"
              }
              multiline
              value={additionalNote}
              onChangeText={setAdditionalNote}
            />
          </View>

          {/* Payment Details */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Payment details</Text>
            <TouchableOpacity style={styles.paymentOption}>
              <View style={styles.radioContainer}>
                <View style={styles.radioOuterSelected}>
                  <View style={styles.radioInner} />
                </View>
                <Text style={styles.paymentMethodText}>
                  {orderType === "delivery"
                    ? "Cash on delivery"
                    : "Cash on pickup"}
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Cart Summary - Using local state values */}
          <CartSummary
            cartItems={cartItems}
            subtotal={localSubtotal}
            tax={localTax}
            total={localTotal}
            currencyCode={currency}
            deliveryCharges={localDeliveryCharges}
            discount={localDiscount}
            couponDiscount={localCouponDiscount}
            tip={localTip}
          />

          {/* Bottom spacing */}
          <View style={{ height: 80 }} />
        </ScrollView>

        {/* Place Order Button */}
        <View style={styles.placeOrderButtonContainer}>
          <TouchableOpacity
            style={styles.placeOrderButton}
            onPress={handlePlaceOrder}
            disabled={processingOrder || getCartCalculationsLoading}
          >
            {processingOrder || getCartCalculationsLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.placeOrderButtonText}>Place Order</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === "success" ? styles.successToast : styles.errorToast,
          ]}
        >
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}

      {/* Address Selection Modal */}
      <AddressSelectionModal
        visible={showAddressModal}
        onClose={() => setShowAddressModal(false)}
        onSave={handleAddressSelection}
        initialAddress={{
          streetAddress: "",
          area: userArea || "",
          city: userCity || "",
          country: userCountry || "",
          postalCode: userPostalCode || "",
          location: userLocation || { lat: "0", lng: "0" },
        }}
        deliverySettings={businessDetails?.deliverySettings}
        cartData={cartData}
        businessId={businessId}
        branchId={branchId}
        orderType={orderType}
        userInfo={{
          name: fullName,
          email,
          phone,
          location: userLocation || { lat: "0", lng: "0" },
          country: userCountry || "",
          postalCode: userPostalCode || "",
        }}
      />

      {/* Order Confirmation Modal */}
      <OrderConfirmationModal
        visible={showOrderConfirmation}
        onClose={handleOrderConfirmationClose}
        orderId={orderId}
      />
      {/* Pickup Schedule Modal */}
      <OrderScheduleModal
        visible={showPickupScheduleModal}
        onClose={() => setShowPickupScheduleModal(false)}
        onSave={handlePickupScheduleSelection}
        initialSchedule={pickupSchedule}
        pickupSlots={pickupSlots || []}
        isLoading={pickupSlotsLoading}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
    marginTop: 32, // Increased from 16 to 32 for more space between header and content
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#F0F0F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    color: "#000000",
  },
  inputLabel: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 4,
    padding: 12,
    fontSize: 14,
    marginBottom: 16,
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  inputDisabled: {
    backgroundColor: "#F5F5F5",
    color: "#666666",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 12,
    marginTop: -12,
    marginBottom: 16,
  },
  addressInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  addressText: {
    color: "#000000",
    flex: 1,
  },
  addressPlaceholder: {
    color: "#999999",
    flex: 1,
  },
  paymentOption: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 4,
    padding: 12,
  },
  radioContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  radioOuterSelected: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#000",
    alignItems: "center",
    justifyContent: "center",
  },
  radioInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: "#000",
  },
  paymentMethodText: {
    marginLeft: 10,
    fontSize: 16,
  },
  placeOrderButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 8,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
  },
  placeOrderButton: {
    backgroundColor: "#000000",
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 80,
    marginHorizontal: 16,
  },
  placeOrderButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#000000",
  },
  pickupLocationContainer: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#F9F9F9",
  },
  pickupLocationText: {
    color: "#000000",
  },
  schedulePickupContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#F0F7FF",
  },
  schedulePickupText: {
    color: "#000000",
    flex: 1,
  },
  editButton: {
    color: "#0066CC",
    fontWeight: "500",
  },
  toast: {
    position: "absolute",
    bottom: 100,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 80,
    alignItems: "center",
    zIndex: 100,
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 1)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 0, 0, 1)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default CheckoutScreen;
