"use client";
import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Animated,
  Alert,
  StyleSheet,
  Dimensions,
  ScrollView,
  Modal,
  ActivityIndicator,
  Platform,
} from "react-native";
import Swiper from "react-native-swiper";
import { MaterialIcons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import DropDownPicker from "react-native-dropdown-picker";
import {
  setSelectedBranch,
  setOrderType,
  resetCartData,
  resetUniqueOrderId,
  setThemeData,
} from "../store/orderSlice";
import {
  useAddToCart,
  formatPrice,
  getFirstOptionPrice,
} from "../utils/cartUtils";
import { useTheme } from "../theme/ThemeProvider";
import CartScreen from "./CartScreen";
import { API_ENDPOINTS } from "../config"; // Import API endpoints from config
import {
  useNavigation,
  CompositeNavigationProp,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { BottomTabNavigationProp } from "@react-navigation/bottom-tabs";
import CustomDrawer from "../components/CustomDrawer";
import { Card } from "../components/ui/Card";
import { Button } from "../components/ui/BUtton";
import { checkBusinessHours } from "../utils/businessHoursUtils";
import Ticker from "../components/Ticker";

const { width } = Dimensions.get("window");

// Helper function to strip HTML tags
const stripHtmlTags = (html) => {
  if (!html) return "";
  return html.replace(/(<([^>]+)>)/gi, "");
};

// Define the navigation type
type HomeScreenNavigationProp = StackNavigationProp<{
  Main: { screen: string; params?: any };
  ItemDetail: { item: any };
  Search: undefined;
  Checkout: undefined;
  Splash: undefined;
}>;

// Define Redux state types
interface OrderState {
  allCategories: any[];
  allItems: any[];
  selectedBranch: any;
  businessId: string;
  orderType: string;
  allBranches: any[];
  hasDelivery: boolean;
  hasPickup: boolean;
  currency: string;
  banners: any[];
  cart: any;
  logo: string;
  businessHours: any[];
  username: string;
}

interface RootState {
  order: OrderState;
  auth: {
    isLoggedIn: boolean;
  };
}

const HomeScreen = () => {
  const { theme, typography } = useTheme();
  const dispatch = useDispatch();
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // Animation values
  const [bannerHeight] = useState(new Animated.Value(180)); // Changed from 220
  const [scrollY] = useState(new Animated.Value(0));

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [loadingItems, setLoadingItems] = useState({});
  const [bannerImages, setBannerImages] = useState([]);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("success"); // success or error
  const [themeData, setThemeData] = useState(null);
  const [rawThemeData, setRawThemeData] = useState(null);
  // Define types for featured categories and products
  type FeaturedCategory = {
    id: string;
    name: string;
    image: string;
    description?: string;
  };

  type FeaturedProduct = {
    name: string;
    items: any[];
  };

  const [featuredCategories, setFeaturedCategories] = useState<
    FeaturedCategory[]
  >([]);
  // Add a state variable for showing banners
  const [showBanners, setShowBanners] = useState(false);
  const [featuredProducts, setFeaturedProducts] = useState<FeaturedProduct[]>(
    []
  );

  // Define type for branch items
  type BranchItem = {
    label: string;
    value: string | null;
  };

  // Branch selector dropdown state
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<BranchItem[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState("");

  // Refs
  const sectionListRef = useRef(null);
  const categoryListRef = useRef(null);

  // Redux state
  const {
    selectedBranch,
    businessId,
    orderType,
    hasDelivery,
    hasPickup,
    currency,
    cart,
    logo, // Added logo to the useSelector
    businessHours, // Get business hours from Redux
    username,
  } = useSelector((state: RootState) => state.order);

  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

  // Use the useAddToCart hook
  const { addToCart, isLoading: isAddToCartLoading } = useAddToCart();

  // Add a debug function to check business hours status
  // Add this function to the HomeScreen component to better debug business hours
  const debugBusinessHours = () => {
    if (businessHours && Array.isArray(businessHours)) {
      console.log(
        "Current business hours status:",
        businessHours.map((hour) => ({
          condition: hour.condition1,
          status: hour.status,
          day: hour.day,
          time: hour.time,
          start_time: hour.start_time,
          end_time: hour.end_time,
        }))
      );

      // Check both condition1 and status
      const isClosedByCondition = businessHours.some(
        (hours) => hours.condition1 === "close"
      );
      const isClosedByStatus = businessHours.some(
        (hours) => hours.status === "0"
      );

      console.log("Business is closed by condition:", isClosedByCondition);
      console.log("Business is closed by status:", isClosedByStatus);
      console.log(
        "Business is currently closed:",
        isClosedByCondition || isClosedByStatus
      );

      // Get current time in Pakistan
      const now = new Date();
      const pakistanTime = new Date(now.getTime() + 5 * 60 * 60 * 1000);
      console.log("Current time (Pakistan):", pakistanTime.toISOString());

      // Format time for comparison
      const hours = pakistanTime.getUTCHours();
      const minutes = pakistanTime.getUTCMinutes();
      const currentTimeString = `${String(hours).padStart(2, "0")}:${String(
        minutes
      ).padStart(2, "0")}`;
      console.log("Current time string for comparison:", currentTimeString);

      // Check if current time is within business hours
      businessHours.forEach((hour) => {
        if (hour.start_time && hour.end_time) {
          console.log(
            `Comparing: ${currentTimeString} with business hours ${hour.start_time} - ${hour.end_time}`
          );
          const isBeforeOpening = currentTimeString < hour.start_time;
          const isAfterClosing = currentTimeString > hour.end_time;
          console.log(
            `Before opening: ${isBeforeOpening}, After closing: ${isAfterClosing}`
          );
        }
      });
    } else {
      console.log("No business hours data available");
    }
  };

  // Call this function when the component mounts and before adding to cart
  useEffect(() => {
    debugBusinessHours();
  }, [businessHours]);

  // Update the fetchThemeData function to add more logging
  const fetchThemeData = async (username) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `${API_ENDPOINTS.RESTAURANT_THEME}?user_name=${username}&flag=0`
      );
      const data = await response.json();

      // Store the raw API response for Ticker component
      setRawThemeData(data.result);

      if (data.result && data.result.web_theme) {
        try {
          // Parse the web_theme string to JSON
          const parsedTheme = JSON.parse(data.result.web_theme);
          console.log("Parsed theme data successfully");
          setThemeData(parsedTheme);

          // Process banners - check if banner array exists
          if (parsedTheme.banner && Array.isArray(parsedTheme.banner)) {
            // Filter banners where source is 'all' or 'app' (case-insensitive) and status is 1
            const filteredBanners = parsedTheme.banner.filter(
              (banner) =>
                banner.status === 1 &&
                banner.source &&
                (banner.source.toLowerCase() === "all" ||
                  banner.source.toLowerCase() === "app")
            );

            console.log("Filtered banners:", filteredBanners);

            if (filteredBanners.length > 0) {
              setBannerImages(
                filteredBanners.map((banner) => banner.image_url)
              );
              setShowBanners(true);
            } else {
              setBannerImages([]);
              setShowBanners(false);
            }
          }

          // Process sections for featured categories and featured products
          if (parsedTheme.sections) {
            console.log("Found sections in theme data");
            processSections(parsedTheme.sections);
          } else {
            console.log("No sections found in theme data");

            // If no sections in parsedTheme, try looking for sections in the home property
            if (parsedTheme.home && parsedTheme.home.sections) {
              console.log("Found sections in home property");
              processSections(parsedTheme.home.sections);
            } else {
              console.log("No sections found in home property either");
              setFeaturedCategories([]);
              setFeaturedProducts([]);
            }
          }
        } catch (error) {
          console.error("Error parsing web_theme:", error);
          setBannerImages([]);
          setShowBanners(false);
          setFeaturedCategories([]);
          setFeaturedProducts([]);
        }
      } else {
        console.log("No web_theme found in API response");
      }
    } catch (error) {
      console.error("Error fetching theme data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Update the processSections function to better handle the data structure
  const processSections = (sections) => {
    if (!sections) return;

    console.log("Processing sections:", sections);
    const categories = [];
    const products = [];

    try {
      // Iterate through section keys
      Object.keys(sections).forEach((key) => {
        const section = sections[key];

        // Check if this is a valid section to process
        if (section && typeof section === "object") {
          // Process featured categories
          if (section.type === "category") {
            console.log("Found category section:", section);

            // Check for featured_category in different possible locations
            let categoryData = null;
            if (section.featured_category && section.featured_category.result) {
              categoryData = section.featured_category.result;
            } else if (section.featured_category) {
              categoryData = section.featured_category;
            }

            if (categoryData) {
              console.log("Found category data:", categoryData);

              // Ensure categoryData is an array
              const categoryArray = Array.isArray(categoryData)
                ? categoryData
                : [categoryData];

              categoryArray.forEach((category) => {
                if (category && category.category_id) {
                  categories.push({
                    id: category.category_id,
                    name: category.category_name || "Category",
                    image: category.image || "",
                    description: category.description || "",
                  });
                }
              });
            }
          }
          // Process featured products
          else if (section.type === "featured") {
            // Check for featured_products in different possible locations
            let productsData = null;
            if (section.featured_products) {
              productsData = section.featured_products;
            }

            if (productsData) {
              // Ensure productsData is an array
              const productsArray = Array.isArray(productsData)
                ? productsData
                : [productsData];

              if (productsArray.length > 0) {
                // Add section name and products to the list
                products.push({
                  name: section.app_name || section.name || "Featured Products",
                  items: productsArray,
                });
              }
            }
          }
        }
      });

      setFeaturedCategories(categories);
      setFeaturedProducts(products);
    } catch (error) {
      console.error("Error processing sections:", error);
      setFeaturedCategories([]);
      setFeaturedProducts([]);
    }
  };

  // Process featured categories from theme data

  // Fetch theme data when username is available
  useEffect(() => {
    if (username) {
      fetchThemeData(username);
    }
  }, [username]);

  // Determine availability based on selectedBranch
  const deliveryAvailable = selectedBranch && selectedBranch.delivery === "1";
  const pickupAvailable = selectedBranch && selectedBranch.pickup === "1";

  // Add a state variable to track if multiple branches are available
  const [hasMultipleBranches, setHasMultipleBranches] = useState(false);

  useEffect(() => {
    fetchBranches();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.BRANCHES}?source=app&restaurant_id=${businessId}`
      );
      const data = await response.json();

      // Check if there are multiple branches
      setHasMultipleBranches(data.result.length > 1);

      const branchItems = data.result.map((branch) => ({
        label: `${branch.address}, ${branch.area}, ${branch.city}`,
        value: branch.branch_id,
      }));
      setItems([{ label: "Select a branch", value: null }, ...branchItems]);

      // If there's only one branch, select it automatically without showing the modal
      if (data.result.length === 1) {
        const singleBranch = data.result[0];
        dispatch(setSelectedBranch(singleBranch));
        setSelectedBranchId(singleBranch.branch_id);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  // Calculate total item count
  const getTotalItemCount = () => {
    if (cart && cart.items) {
      return cart.items.reduce((total, item) => total + (item.dqty || 0), 0);
    }
    return 0;
  };

  // Show toast message
  const showToastMessage = (message, type = "success") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 2000);
  };

  // Update the handleAddToCart function to call debugBusinessHours
  const handleAddToCart = async (product) => {
    // Check if business is closed
    console.log("Checking business hours before adding to cart");
    debugBusinessHours();

    if (checkBusinessHours(businessHours)) {
      console.log("Business is closed, cannot add to cart");
      return; // Don't proceed if business is closed
    }

    console.log("Business is open, proceeding with add to cart");

    setLoadingItems((prevState) => ({
      ...prevState,
      [product.menu_item_id]: true,
    }));

    // Check if the item has options
    const hasValidOptions = product.options && product.options.length > 0;

    if (hasValidOptions) {
      // If item has options, navigate to detail screen
      navigation.navigate("ItemDetail", { item: product });
      setLoadingItems((prevState) => ({
        ...prevState,
        [product.menu_item_id]: false,
      }));
      return;
    }

    // If no options, add directly to cart
    const result = await addToCart(product, "add", "new");

    if (!result.success) {
      Alert.alert("Error", result.message);
    } else {
      // Show toast message
      showToastMessage("Item added successfully", "success");
    }

    setLoadingItems((prevState) => ({
      ...prevState,
      [product.menu_item_id]: false,
    }));
  };

  const onProceed = () => {
    if (!selectedBranchId) {
      setErrorMessage("Please select a branch to proceed.");
      return;
    }

    dispatch(
      setSelectedBranch(items.find((item) => item.value === selectedBranchId))
    );
    setModalVisible(false);
  };

  // Handle cart closed with empty cart
  const handleCartClosed = (isEmpty) => {
    setIsCartVisible(false);
    if (isEmpty) {
      showToastMessage("Cart empty", "error");
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: "#FFFFFF" }]}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === "android" ? "transparent" : "#FFFFFF"}
        translucent={Platform.OS === "android"}
      />

      {/* Header with location and toggle */}
      <View style={styles.headerContainer}>
        {/* Left side with menu and location */}
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={() => setIsDrawerVisible(true)}
            style={styles.menuButton}
          >
            <MaterialIcons name="menu" size={24} color="#000000" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // Only proceed if there are multiple branches
              if (hasMultipleBranches) {
                // Check if cart is empty
                const isCartEmpty = !cart?.items || cart.items.length === 0;

                if (!isCartEmpty) {
                  // If cart is not empty, show confirmation alert
                  Alert.alert(
                    "Change Branch",
                    "Changing the branch will empty your cart. Do you want to continue?",
                    [
                      {
                        text: "No",
                        style: "cancel",
                      },
                      {
                        text: "Yes",
                        onPress: () => {
                          // Empty the cart and reset unique order ID
                          dispatch(resetCartData());
                          dispatch(resetUniqueOrderId());
                          navigation.navigate("Splash");
                        },
                      },
                    ]
                  );
                } else {
                  // If cart is empty, directly navigate to splash screen
                  navigation.navigate("Splash");
                }
              }
            }}
            style={[
              styles.locationSelector,
              !hasMultipleBranches && styles.disabledLocationSelector,
            ]}
          >
            <Text style={styles.deliveryLabel}>
              {orderType === "delivery" ? "Delivering from" : "Pickup from"}
              {hasMultipleBranches && (
                <MaterialIcons
                  name="keyboard-arrow-down"
                  size={16}
                  color="#000000"
                />
              )}
            </Text>
            <Text style={styles.locationText} numberOfLines={1}>
              {selectedBranch && selectedBranch.address
                ? selectedBranch.address
                : "Select location"}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Right side with order type toggle */}
        <View style={styles.headerRight}>
          <View style={styles.orderTypeToggleContainer}>
            <TouchableOpacity
              onPress={() => {
                if (deliveryAvailable) {
                  dispatch(setOrderType("delivery"));
                } else {
                  showToastMessage(
                    "Delivery is not available for this branch",
                    "error"
                  );
                }
              }}
              style={[
                styles.orderTypeButton,
                orderType === "delivery"
                  ? styles.activeOrderTypeButton
                  : styles.inactiveOrderTypeButton,
                !deliveryAvailable && styles.disabledOrderTypeButton,
              ]}
            >
              <MaterialIcons
                name="pedal-bike"
                size={18}
                color={
                  orderType === "delivery"
                    ? "#FFFFFF"
                    : !deliveryAvailable
                    ? "#AAAAAA"
                    : "#000000"
                }
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                if (pickupAvailable) {
                  dispatch(setOrderType("pickup"));
                } else {
                  showToastMessage(
                    "Pickup is not available for this branch",
                    "error"
                  );
                }
              }}
              style={[
                styles.orderTypeButton,
                orderType === "pickup"
                  ? styles.activeOrderTypeButton
                  : styles.inactiveOrderTypeButton,
                !pickupAvailable && styles.disabledOrderTypeButton,
              ]}
            >
              <MaterialIcons
                name="store"
                size={18}
                color={
                  orderType === "pickup"
                    ? "#FFFFFF"
                    : !pickupAvailable
                    ? "#AAAAAA"
                    : "#000000"
                }
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Ticker Component - Passing raw theme data from API */}
      <Ticker themeData={rawThemeData} />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => navigation.navigate("Search")}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name="search"
            size={24}
            color="#777777"
            style={styles.searchIcon}
          />
          <Text style={styles.searchPlaceholder}>Search here</Text>
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000000" />
        </View>
      ) : (
        <FlatList
          data={[{ key: "content" }]}
          renderItem={() => (
            <View style={styles.contentContainer}>
              {/* Banner Slider - Only show if banners should be shown */}
              {showBanners && bannerImages.length > 0 && (
                <View style={styles.bannerContainer}>
                  <Swiper
                    style={styles.wrapper}
                    showsButtons={false}
                    autoplay={true}
                    loop={true}
                    dotStyle={styles.dot}
                    activeDotStyle={styles.activeDot}
                    paginationStyle={styles.pagination}
                  >
                    {bannerImages.map((banner, index) => (
                      <View style={styles.bannerItem} key={index}>
                        <Image
                          source={{
                            uri:
                              typeof banner === "string"
                                ? banner
                                : banner.image_url,
                          }}
                          style={styles.bannerImage}
                          resizeMode="cover"
                        />
                      </View>
                    ))}
                  </Swiper>
                </View>
              )}

              {/* Featured Categories - Only show if there are featured categories */}
              {featuredCategories.length > 0 && (
                <View style={styles.featuredCategoriesSection}>
                  <Text style={styles.sectionTitle}>Featured Categories</Text>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.categoriesScroll}
                    contentContainerStyle={styles.categoriesScrollContent}
                  >
                    {featuredCategories.map((category, index) => (
                      <TouchableOpacity
                        key={category.id || index}
                        style={styles.categoryItem}
                        onPress={() => {
                          // Navigate to the ShopTab using the correct navigation path
                          navigation.navigate("Main", {
                            screen: "ShopTab",
                            params: {
                              selectedCategoryId: category.id,
                            },
                          });
                        }}
                      >
                        <View style={styles.categoryImageContainer}>
                          <Image
                            source={
                              category.image &&
                              !category.image.includes("no_image")
                                ? { uri: category.image }
                                : require("../assets/place_holder.png")
                            }
                            style={styles.categoryImage}
                          />
                        </View>
                        <Text style={styles.categoryName} numberOfLines={1}>
                          {category.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}

              {/* Featured Products from Theme API */}
              {featuredProducts.map((section, sectionIndex) => (
                <View
                  key={`section-${sectionIndex}`}
                  style={styles.featuredItemsSection}
                >
                  <Text style={styles.sectionTitle}>{section.name}</Text>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.itemsScroll}
                    contentContainerStyle={styles.itemsScrollContent}
                  >
                    {section.items.map((item, index) => {
                      const isItemLoading = loadingItems[item.menu_item_id];
                      const hasDiscount = item.discount && item.discount > 0;

                      // Get the price to display - either the product price or the first option price
                      const {
                        price: displayPrice,
                        showFromText,
                        isAvailable,
                      } = getFirstOptionPrice(item);

                      const discountedPrice = hasDiscount
                        ? displayPrice - displayPrice * (item.discount / 100)
                        : displayPrice;

                      // Determine if the product is unavailable (status === "1")
                      const isUnavailable = !isAvailable;

                      return (
                        <TouchableOpacity
                          key={item.menu_item_id || index}
                          style={styles.productCard}
                          onPress={() =>
                            navigation.navigate("ItemDetail", { item })
                          }
                          activeOpacity={0.9}
                          disabled={isUnavailable}
                        >
                          {/* Grey overlay for unavailable products */}
                          {isUnavailable && (
                            <View style={styles.unavailableProductOverlay} />
                          )}

                          {/* Product Image */}
                          <View style={styles.productImageContainer}>
                            {item.image && !item.image.includes("no_image") ? (
                              <Image
                                source={{ uri: item.image }}
                                style={styles.productImage}
                                resizeMode="cover"
                              />
                            ) : (
                              <View style={styles.noImageContainer}>
                                {logo ? (
                                  <Image
                                    source={{ uri: logo }}
                                    style={{ width: 40, height: 40 }}
                                    resizeMode="contain"
                                  />
                                ) : (
                                  <MaterialIcons
                                    name="image"
                                    size={30}
                                    color="#666"
                                  />
                                )}
                              </View>
                            )}
                          </View>

                          {/* Product Title */}
                          <Text
                            style={styles.productTitle}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >
                            {item.name}
                          </Text>

                          {/* Product Description */}
                          <Text
                            style={styles.productDescription}
                            numberOfLines={2}
                            ellipsizeMode="tail"
                          >
                            {item.desc ? stripHtmlTags(item.desc) : ""}
                          </Text>

                          {/* Price and Add Button Row */}
                          <View style={styles.priceActionRow}>
                            <View style={styles.priceContainer}>
                              {isUnavailable ? (
                                <Text
                                  style={[
                                    styles.currentPrice,
                                    { color: "#FF3B30" },
                                  ]}
                                >
                                  Not Available
                                </Text>
                              ) : hasDiscount ? (
                                <View style={styles.priceRow}>
                                  <Text style={styles.currentPrice}>
                                    {showFromText ? "From " : ""}
                                    {formatPrice(discountedPrice, currency)}
                                  </Text>
                                  <Text style={styles.originalPrice}>
                                    {formatPrice(item.price, currency)}
                                  </Text>
                                </View>
                              ) : (
                                <Text style={styles.currentPrice}>
                                  {showFromText ? "From " : ""}
                                  {formatPrice(discountedPrice, currency)}
                                </Text>
                              )}
                            </View>

                            {/* Only show add button for available products */}
                            {!isUnavailable && (
                              <TouchableOpacity
                                style={styles.addButton}
                                onPress={(e) => {
                                  e.stopPropagation();
                                  handleAddToCart(item);
                                }}
                                disabled={isItemLoading}
                              >
                                {isItemLoading ? (
                                  <ActivityIndicator
                                    size="small"
                                    color="#FFFFFF"
                                  />
                                ) : (
                                  <MaterialIcons
                                    name="add"
                                    size={20}
                                    color="#FFFFFF"
                                  />
                                )}
                              </TouchableOpacity>
                            )}
                          </View>
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                </View>
              ))}
            </View>
          )}
          keyExtractor={(item) => item.key}
        />
      )}

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === "success" ? styles.successToast : styles.errorToast,
          ]}
        >
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}

      {/* Cart Modal */}
      <CartScreen
        visible={isCartVisible}
        onClose={(isEmpty) => handleCartClosed(isEmpty)}
        onProceedToCheckout={() => navigation.navigate("Checkout")}
      />

      {/* Branch Selection Modal */}
      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <Card style={styles.modalContent} elevation={3}>
            <Text
              style={[
                styles.modalTitle,
                { color: theme.text, ...typography.h3 },
              ]}
            >
              Choose Your Preference
            </Text>

            <View style={styles.toggleContainer}>
              <View style={[styles.iconToggle, { borderColor: theme.border }]}>
                {hasDelivery && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType("delivery"))}
                    style={[
                      styles.iconButton,
                      orderType === "delivery"
                        ? { backgroundColor: theme.primary }
                        : { backgroundColor: theme.surface },
                    ]}
                  >
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === "delivery"
                          ? { color: "#FFFFFF", fontWeight: "bold" }
                          : { color: theme.textSecondary },
                      ]}
                    >
                      Delivery
                    </Text>
                  </TouchableOpacity>
                )}

                {hasPickup && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType("pickup"))}
                    style={[
                      styles.iconButton,
                      orderType === "pickup"
                        ? { backgroundColor: theme.primary }
                        : { backgroundColor: theme.surface },
                    ]}
                  >
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === "pickup"
                          ? { color: "#FFFFFF", fontWeight: "bold" }
                          : { color: theme.textSecondary },
                      ]}
                    >
                      Pickup
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <Text
              style={[
                styles.sectionTitle,
                { color: theme.text, ...typography.h5 },
              ]}
            >
              Select Branch
            </Text>

            <View style={styles.pickerContainer}>
              <DropDownPicker
                open={open}
                value={selectedBranchId}
                items={items}
                setOpen={setOpen}
                setValue={setSelectedBranchId}
                setItems={setItems}
                placeholder="Select a branch"
                style={[
                  styles.dropdown,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                dropDownContainerStyle={[
                  styles.dropdownContainer,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                textStyle={[styles.dropdownText, { color: theme.text }]}
                placeholderStyle={[
                  styles.placeholderText,
                  { color: theme.textLight },
                ]}
                onChangeValue={(value) => {
                  setSelectedBranchId(value);
                  if (value) {
                    setErrorMessage("");
                  }
                }}
                listItemLabelStyle={[
                  styles.listItemLabel,
                  { color: theme.text },
                ]}
                selectedItemLabelStyle={[
                  styles.selectedItemLabel,
                  { color: theme.primary },
                ]}
                maxHeight={150}
              />
            </View>

            {errorMessage !== "" && (
              <Text style={[styles.errorText, { color: theme.error }]}>
                {errorMessage}
              </Text>
            )}

            <Button
              title="Start My Order"
              onPress={onProceed}
              disabled={!selectedBranchId}
              fullWidth
              size="large"
              style={styles.startButton}
            />
          </Card>
        </View>
      </Modal>

      {/* Custom Drawer */}
      <CustomDrawer
        visible={isDrawerVisible}
        onClose={() => setIsDrawerVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop:
      Platform.OS === "android" ? (StatusBar.currentHeight || 0) + 12 : 12,
    backgroundColor: "#FFFFFF",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  menuButton: {
    padding: 4,
    marginRight: 12,
  },
  locationSelector: {
    flex: 0,
  },
  deliveryLabel: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 2,
    flexDirection: "row",
    alignItems: "center",
    fontFamily: "PlusJakartaSans-Regular",
  },
  locationText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#000000",
    fontFamily: "PlusJakartaSans-Bold",
    width: 160,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  toggleContainer: {
    backgroundColor: "#000000",
    borderRadius: 20,
    padding: 8,
    marginRight: 12,
  },
  profileButton: {
    backgroundColor: "#F5F5F5",
    borderRadius: 20,
    padding: 8,
  },
  searchContainer: {
    marginTop: 8,
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchPlaceholder: {
    color: "#999999",
    fontSize: 16,
    fontFamily: "PlusJakartaSans-Regular",
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  bannerContainer: {
    height: 180,
    marginBottom: 16,
    marginHorizontal: 16, // Changed from 8 to 16
    borderRadius: 12,
    overflow: "hidden",
  },
  wrapper: {},
  bannerItem: {
    flex: 1,
  },
  bannerImage: {
    width: "100%",
    height: "100%",
    borderRadius: 12, // Added rounded corners
  },
  bannerPlaceholder: {
    width: "100%",
    height: "100%",
    backgroundColor: "#F0F0F0",
    borderRadius: 12,
  },
  dot: {
    backgroundColor: "rgba(255, 255, 255, 0.4)",
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: "#FFFFFF",
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 4,
  },
  pagination: {
    bottom: 10,
  },
  featuredCategoriesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginHorizontal: 16,
    marginBottom: 16,
    fontFamily: "PlusJakartaSans-Bold",
  },
  categoriesScroll: {
    flexGrow: 0,
  },
  categoriesScrollContent: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  categoryItem: {
    alignItems: "center",
    marginRight: 16,
    width: 90,
  },
  categoryImageContainer: {
    width: 90,
    height: 90,
    borderRadius: 8,
    overflow: "hidden",
    marginBottom: 8,
  },
  categoryImage: {
    width: "100%",
    height: "100%",
  },
  categoryName: {
    fontSize: 14,
    textAlign: "center",
    color: "#000000",
    fontFamily: "PlusJakartaSans-Medium",
  },
  bottomNavigation: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: "#EEEEEE",
    paddingVertical: 8,
    backgroundColor: "#FFFFFF",
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
  },
  navItemText: {
    fontSize: 12,
    color: "#AAAAAA",
    marginTop: 4,
    fontFamily: "PlusJakartaSans-Regular",
  },
  navItemTextActive: {
    fontSize: 12,
    color: "#000000",
    fontWeight: "bold",
    marginTop: 4,
    fontFamily: "PlusJakartaSans-Bold",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 20,
  },
  modalContent: {
    width: "100%",
    maxWidth: 400,
    padding: 24,
    minHeight: 380,
  },
  modalTitle: {
    marginBottom: 24,
    textAlign: "center",
    fontFamily: "PlusJakartaSans-Bold",
  },
  iconToggle: {
    flexDirection: "row",
    borderRadius: 8,
    borderWidth: 1,
    overflow: "hidden",
  },
  iconButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    width: 120,
  },
  iconLabel: {
    fontFamily: "PlusJakartaSans-Medium",
    fontSize: 14,
  },
  pickerContainer: {
    marginBottom: 16,
    zIndex: 1000,
  },
  dropdown: {
    borderRadius: 8,
    minHeight: 50,
  },
  dropdownContainer: {
    borderRadius: 8,
  },
  dropdownText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  placeholderText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  listItemLabel: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  selectedItemLabel: {
    fontFamily: "PlusJakartaSans-Medium",
    fontWeight: "600",
  },
  errorText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 12,
    marginBottom: 16,
  },
  startButton: {
    marginTop: 8,
  },
  orderTypeToggleContainer: {
    flexDirection: "row",
    backgroundColor: "#F5F5F5",
    borderRadius: 24,
    overflow: "hidden",
  },
  orderTypeButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  activeOrderTypeButton: {
    backgroundColor: "#000000",
  },
  inactiveOrderTypeButton: {
    backgroundColor: "transparent",
  },
  featuredItemsSection: {
    marginBottom: 24,
  },
  itemsScroll: {
    flexGrow: 0,
  },
  itemsScrollContent: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  // Original product card styles
  productCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    overflow: "hidden",
    width: 180,
    marginRight: 16,
    minHeight: 230,
    elevation: 3,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 1,
  },
  productImageContainer: {
    width: "100%",
    height: 140,
    backgroundColor: "#F8F8F8",
  },
  productImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  noImageContainer: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F8F8F8",
  },
  productTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 8,
    fontFamily: "PlusJakartaSans-SemiBold",
  },
  productDescription: {
    fontSize: 12,
    color: "#666666",
    paddingHorizontal: 8,
    marginBottom: 4,
    lineHeight: 16,
    height: 32,
    fontFamily: "PlusJakartaSans-Regular",
  },
  priceActionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingBottom: 8,
    marginTop: 16,
  },
  priceContainer: {
    flexDirection: "column",
  },
  priceRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  originalPrice: {
    fontSize: 13,
    color: "#888888",
    textDecorationLine: "line-through",
    fontFamily: "PlusJakartaSans-Regular",
    marginLeft: 8,
  },
  currentPrice: {
    fontSize: 15,
    fontWeight: "bold",
    color: "#000000",
    fontFamily: "PlusJakartaSans-Bold",
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#000000",
    justifyContent: "center",
    alignItems: "center",
  },
  cartIconContainer: {
    position: "relative",
  },
  cartBadge: {
    position: "absolute",
    top: -8,
    right: -8,
    backgroundColor: "#FF3B30",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
    fontFamily: "PlusJakartaSans-Bold",
  },
  disabledLocationSelector: {
    opacity: 0.8,
  },
  toast: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 100,
    alignItems: "center",
    marginBottom: 20, // Added bottom margin
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 1)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 0, 0, 1)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "PlusJakartaSans-Medium",
  },
  disabledOrderTypeButton: {
    opacity: 0.5,
  },
  // Styles for unavailable products
  unavailableProductOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.6)",
    zIndex: 10,
    borderRadius: 8,
  },
});

export default HomeScreen;
