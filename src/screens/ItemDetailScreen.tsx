"use client";

import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
  StatusBar,
  Animated,
  SafeAreaView,
  Modal,
  ToastAndroid,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useSelector } from "react-redux";
import { formatPrice, useAddToCart } from "../utils/cartUtils";
import { useTheme } from "../theme/ThemeProvider";
import OptionSetRenderer from "../components/OptionSetRenderer";
import { checkBusinessHours } from "../utils/businessHoursUtils";
import { ImageZoom } from "@likashefqet/react-native-image-zoom";
import CartScreen from "./CartScreen";

const { width, height } = Dimensions.get("window");
const STATUSBAR_HEIGHT =
  Platform.OS === "android" ? StatusBar.currentHeight || 0 : 0;

const ItemDetailScreen = ({ route, navigation }) => {
  const { item } = route.params;
  const { theme } = useTheme();
  const scrollViewRef = useRef(null);

  // Animation for error message
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [errorMessage, setErrorMessage] = useState("");

  // State
  const [quantity, setQuantity] = useState(1);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [expandedOptions, setExpandedOptions] = useState({});
  const [highlightedOptions, setHighlightedOptions] = useState([]);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [isImageViewerVisible, setIsImageViewerVisible] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("success");

  // Redux state
  const { currency, businessHours, cart } = useSelector((state) => state.order);

  // Function to check if cart is empty
  const isCartEmpty = () => {
    return !cart?.items || cart.items.length === 0;
  };

  // State for cart visibility
  const [isCartVisible, setIsCartVisible] = useState(false);

  // Show toast message
  const showToastMessage = (message, type = "error") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);

    // Also show native toast on Android for better UX
    if (Platform.OS === "android") {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    }
  };

  // Function to handle cart icon click
  const handleCartIconClick = () => {
    // Check if cart is empty
    if (isCartEmpty()) {
      // Show toast message if cart is empty
      showToastMessage("Cart is Empty", "error");
    } else {
      // Show the cart modal if cart is not empty
      setIsCartVisible(true);
    }
  };

  // Handle cart closed with empty cart
  const handleCartClosed = (isEmpty) => {
    setIsCartVisible(false);
    if (isEmpty) {
      // Show toast message if needed
      // You could add a toast message here if needed
    }
  };

  // Use the useAddToCart hook
  const { addToCart, isLoading } = useAddToCart();

  // Log item data for debugging
  useEffect(() => {
    if (item) {
      console.log(
        "ItemDetailScreen - Item data:",
        JSON.stringify(item, null, 2)
      );
    }
  }, [item]);

  // Initialize option set details with support for inner options and nested inner options
  useEffect(() => {
    if (item && item.options) {
      initializeOptionSets();

      // Expand all option sets by default
      const initialExpandedState = {};
      item.options.forEach((option) => {
        initialExpandedState[option.id] = true;

        // Also expand inner option sets if they exist
        option.items?.forEach((optionItem) => {
          if (optionItem.inner_options && optionItem.inner_options.length > 0) {
            // First level inner options
            optionItem.inner_options.forEach((innerOptionSet) => {
              const innerKey = `${option.id}_${optionItem.id}_${innerOptionSet.id}`;
              initialExpandedState[innerKey] = true;

              // Check for second level inner options (nested inner options)
              innerOptionSet.items?.forEach((innerItem) => {
                if (
                  innerItem.inner_options &&
                  innerItem.inner_options.length > 0
                ) {
                  innerItem.inner_options.forEach((nestedInnerOptionSet) => {
                    const nestedKey = `${option.id}_${optionItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                    initialExpandedState[nestedKey] = true;
                  });
                }
              });
            });
          }
        });
      });
      setExpandedOptions(initialExpandedState);
    }
  }, [item]);

  // Handle error message animation
  useEffect(() => {
    if (errorMessage) {
      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto hide after 3 seconds
      const timer = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => setErrorMessage(""));
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [errorMessage, fadeAnim]);

  // Initialize option sets with support for inner options and nested inner options
  const initializeOptionSets = () => {
    if (!item || !item.options) return;

    const initialOptions = {};

    item.options.forEach((optionSet) => {
      const isRequired = optionSet.min_quantity !== "0";
      const isMultiSelect =
        optionSet.quantity > 1 || optionSet.quantity === "0";

      initialOptions[optionSet.id] = {
        name: optionSet.name,
        isRequired,
        isMultiSelect,
        items: {},
        innerOptions: {}, // Add storage for inner options
      };

      // Initialize inner option sets if they exist
      optionSet.items?.forEach((optionItem) => {
        // Check if this option item has inner option sets in its 'items' array
        if (
          optionItem.items &&
          Array.isArray(optionItem.items) &&
          optionItem.items.length > 0
        ) {
          optionItem.items.forEach((innerOptionSet) => {
            const innerKey = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}`;
            const isInnerRequired = innerOptionSet.min_quantity !== "0";
            const isInnerMultiSelect =
              innerOptionSet.quantity > 1 || innerOptionSet.quantity === "0";

            initialOptions[innerKey] = {
              name: innerOptionSet.name,
              isRequired: isInnerRequired,
              isMultiSelect: isInnerMultiSelect,
              items: {},
              parentOptionId: optionSet.id,
              parentItemId: optionItem.id,
            };

            // Initialize nested inner option sets if they exist
            innerOptionSet.items?.forEach((innerItem) => {
              if (
                innerItem.items &&
                Array.isArray(innerItem.items) &&
                innerItem.items.length > 0
              ) {
                innerItem.items.forEach((nestedInnerOptionSet) => {
                  const nestedKey = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                  const isNestedRequired =
                    nestedInnerOptionSet.min_quantity !== "0";
                  const isNestedMultiSelect =
                    nestedInnerOptionSet.quantity > 1 ||
                    nestedInnerOptionSet.quantity === "0";

                  initialOptions[nestedKey] = {
                    name: nestedInnerOptionSet.name,
                    isRequired: isNestedRequired,
                    isMultiSelect: isNestedMultiSelect,
                    items: {},
                    parentOptionId: innerKey,
                    parentItemId: innerItem.id,
                    isNestedInner: true,
                  };
                });
              }
            });
          });
        }

        // Also check for inner_options for backward compatibility
        if (optionItem.inner_options && optionItem.inner_options.length > 0) {
          optionItem.inner_options.forEach((innerOptionSet) => {
            const innerKey = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}`;
            const isInnerRequired = innerOptionSet.min_quantity !== "0";
            const isInnerMultiSelect =
              innerOptionSet.quantity > 1 || innerOptionSet.quantity === "0";

            initialOptions[innerKey] = {
              name: innerOptionSet.name,
              isRequired: isInnerRequired,
              isMultiSelect: isInnerMultiSelect,
              items: {},
              parentOptionId: optionSet.id,
              parentItemId: optionItem.id,
            };

            // Initialize nested inner option sets if they exist
            innerOptionSet.items?.forEach((innerItem) => {
              if (
                innerItem.inner_options &&
                innerItem.inner_options.length > 0
              ) {
                innerItem.inner_options.forEach((nestedInnerOptionSet) => {
                  const nestedKey = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                  const isNestedRequired =
                    nestedInnerOptionSet.min_quantity !== "0";
                  const isNestedMultiSelect =
                    nestedInnerOptionSet.quantity > 1 ||
                    nestedInnerOptionSet.quantity === "0";

                  initialOptions[nestedKey] = {
                    name: nestedInnerOptionSet.name,
                    isRequired: isNestedRequired,
                    isMultiSelect: isNestedMultiSelect,
                    items: {},
                    parentOptionId: innerKey,
                    parentItemId: innerItem.id,
                    isNestedInner: true,
                  };
                });
              }
            });
          });
        }
      });
    });

    console.log(
      "Initialized options:",
      JSON.stringify(initialOptions, null, 2)
    );
    setSelectedOptions(initialOptions);
  };

  // Update quantity
  const updateQuantity = (change) => {
    setQuantity((prevQuantity) => Math.max(1, prevQuantity + change));
  };

  // Toggle option expansion with support for inner options
  const toggleOption = (optionId) => {
    setExpandedOptions((prev) => ({
      ...prev,
      [optionId]: !prev[optionId],
    }));
  };

  // Handle option selection with support for inner options and nested inner options
  const handleOptionSelect = (
    optionId,
    itemId,
    isInnerOption = false,
    parentInfo = null
  ) => {
    // Clear highlighted options when user makes a selection
    setHighlightedOptions([]);

    setSelectedOptions((prev) => {
      const updatedOptions = { ...prev };
      const optionSet = updatedOptions[optionId];

      if (!optionSet) return prev;

      // If single select (radio button), replace previous selection
      if (!optionSet.isMultiSelect) {
        updatedOptions[optionId] = {
          ...optionSet,
          items: { [itemId]: 1 },
        };

        // Check if this is a nested inner option, regular inner option, or parent option
        const parts = optionId.split("_");

        if (parts.length === 5) {
          // This is a nested inner option - no need to reset anything else
        } else if (parts.length === 3 && isInnerOption) {
          // This is a regular inner option - reset any nested inner options when inner option changes
          Object.keys(updatedOptions).forEach((key) => {
            if (
              key.startsWith(`${optionId}_${itemId}_`) ||
              (key.startsWith(`${parts[0]}_${parts[1]}_${parts[2]}_`) &&
                !key.startsWith(
                  `${parts[0]}_${parts[1]}_${parts[2]}_${itemId}_`
                ))
            ) {
              updatedOptions[key] = {
                ...updatedOptions[key],
                items: {},
              };
            }
          });
        } else if (!isInnerOption) {
          // This is a parent option - reset all inner options when parent changes
          Object.keys(updatedOptions).forEach((key) => {
            if (
              key.startsWith(`${optionId}_`) &&
              !key.startsWith(`${optionId}_${itemId}_`)
            ) {
              updatedOptions[key] = {
                ...updatedOptions[key],
                items: {},
              };
            }
          });
        }
      }
      // If multi-select (checkbox)
      else {
        // If already selected, unselect it
        if (optionSet.items[itemId]) {
          const { [itemId]: _, ...remainingItems } = optionSet.items;
          updatedOptions[optionId] = {
            ...optionSet,
            items: remainingItems,
          };

          // Check if this is a nested inner option, regular inner option, or parent option
          const parts = optionId.split("_");

          if (parts.length === 5) {
            // This is a nested inner option - no need to reset anything else
          } else if (parts.length === 3 && isInnerOption) {
            // This is a regular inner option - reset any nested inner options related to this inner item
            Object.keys(updatedOptions).forEach((key) => {
              if (key.startsWith(`${optionId}_${itemId}_`)) {
                updatedOptions[key] = {
                  ...updatedOptions[key],
                  items: {},
                };
              }
            });
          } else if (!isInnerOption) {
            // This is a parent option - reset any inner options related to this parent item
            Object.keys(updatedOptions).forEach((key) => {
              if (key.startsWith(`${optionId}_${itemId}_`)) {
                updatedOptions[key] = {
                  ...updatedOptions[key],
                  items: {},
                };
              }
            });
          }
        }
        // Otherwise select it
        else {
          updatedOptions[optionId] = {
            ...optionSet,
            items: {
              ...optionSet.items,
              [itemId]: 1,
            },
          };
        }
      }

      return updatedOptions;
    });
  };

  // Check if option set requirement is met, including inner options and nested inner options
  const isOptionSetRequirementMet = (optionSetId) => {
    // Check if this is a nested inner option set (has 4 parts in the ID)
    const parts = optionSetId.split("_");

    if (parts.length === 5) {
      // This is a nested inner option set
      const [
        parentOptionId,
        parentItemId,
        innerOptionId,
        innerItemId,
        nestedInnerOptionId,
      ] = parts;

      // First check if parent option is selected
      const parentSelected =
        selectedOptions[parentOptionId]?.items?.[parentItemId];
      if (!parentSelected) return true; // If parent not selected, no requirement

      // Then check if inner option is selected
      const innerOptionKey = `${parentOptionId}_${parentItemId}_${innerOptionId}`;
      const innerSelected =
        selectedOptions[innerOptionKey]?.items?.[innerItemId];
      if (!innerSelected) return true; // If inner option not selected, no requirement

      // Find the nested inner option set
      const parentOptionSet = item.options.find(
        (opt) => opt.id === parentOptionId
      );
      if (!parentOptionSet) return true;

      const parentItem = parentOptionSet.items.find(
        (item) => item.id === parentItemId
      );
      if (!parentItem) return true;

      // Check for inner option sets in both 'items' and 'inner_options' arrays
      let innerOptionSet;

      // First check in 'items' array (new structure)
      if (parentItem.items && Array.isArray(parentItem.items)) {
        innerOptionSet = parentItem.items.find(
          (opt) => opt.id === innerOptionId
        );
      }

      // If not found, check in 'inner_options' array (old structure)
      if (!innerOptionSet && parentItem.inner_options) {
        innerOptionSet = parentItem.inner_options.find(
          (opt) => opt.id === innerOptionId
        );
      }

      if (!innerOptionSet) return true;

      const innerItem = innerOptionSet.items.find(
        (item) => item.id === innerItemId
      );
      if (!innerItem) return true;

      // Check for nested inner option sets in both 'items' and 'inner_options' arrays
      let nestedInnerOptionSet;

      // First check in 'items' array (new structure)
      if (innerItem.items && Array.isArray(innerItem.items)) {
        nestedInnerOptionSet = innerItem.items.find(
          (opt) => opt.id === nestedInnerOptionId
        );
      }

      // If not found, check in 'inner_options' array (old structure)
      if (!nestedInnerOptionSet && innerItem.inner_options) {
        nestedInnerOptionSet = innerItem.inner_options.find(
          (opt) => opt.id === nestedInnerOptionId
        );
      }

      if (!nestedInnerOptionSet) return true;

      const selectedItems = selectedOptions[optionSetId]?.items || {};
      const selectedCount = Object.keys(selectedItems).length;
      const minRequired = Number.parseInt(
        nestedInnerOptionSet.min_quantity || "0"
      );

      return selectedCount >= minRequired;
    }
    // Check if this is a regular inner option set (has 3 parts in the ID)
    else if (parts.length === 3) {
      const [parentOptionId, parentItemId, innerOptionId] = parts;

      // First check if parent is selected
      const parentSelected =
        selectedOptions[parentOptionId]?.items?.[parentItemId];
      if (!parentSelected) return true; // If parent not selected, no requirement

      // Find the inner option set
      const parentOptionSet = item.options.find(
        (opt) => opt.id === parentOptionId
      );
      if (!parentOptionSet) return true;

      const parentItem = parentOptionSet.items.find(
        (item) => item.id === parentItemId
      );
      if (!parentItem) return true;

      // Check for inner option sets in both 'items' and 'inner_options' arrays
      let innerOptionSet;

      // First check in 'items' array (new structure)
      if (parentItem.items && Array.isArray(parentItem.items)) {
        innerOptionSet = parentItem.items.find(
          (opt) => opt.id === innerOptionId
        );
      }

      // If not found, check in 'inner_options' array (old structure)
      if (!innerOptionSet && parentItem.inner_options) {
        innerOptionSet = parentItem.inner_options.find(
          (opt) => opt.id === innerOptionId
        );
      }

      if (!innerOptionSet) return true;

      const selectedItems = selectedOptions[optionSetId]?.items || {};
      const selectedCount = Object.keys(selectedItems).length;
      const minRequired = Number.parseInt(innerOptionSet.min_quantity || "0");

      return selectedCount >= minRequired;
    }

    // Regular option set check
    const optionSet = item.options.find((opt) => opt.id === optionSetId);
    const selectedItems = selectedOptions[optionSetId]?.items || {};
    const selectedCount = Object.keys(selectedItems).length;

    if (!optionSet) return true;

    const minRequired = Number.parseInt(optionSet.min_quantity || "0");
    return selectedCount >= minRequired;
  };

  // Validate options before adding to cart, including inner options
  const validateOptions = () => {
    const invalidOptions = [];

    Object.entries(selectedOptions).forEach(([optionId, optionData]) => {
      // Skip validation for inner options if parent is not selected
      if (optionId.includes("_")) {
        const [parentOptionId, parentItemId] = optionId.split("_");
        const parentSelected =
          selectedOptions[parentOptionId]?.items?.[parentItemId];
        if (!parentSelected) return;
      }

      if (optionData.isRequired && Object.keys(optionData.items).length === 0) {
        invalidOptions.push({
          id: optionId,
          name: optionData.name,
        });
      }
    });

    return invalidOptions;
  };

  // Scroll to a specific option set
  const scrollToOption = (optionId) => {
    if (scrollViewRef.current && item.options) {
      // Find the index of the option set
      const index = item.options.findIndex((opt) => opt.id === optionId);
      if (index !== -1) {
        // Calculate approximate position (this is an estimation)
        const optionPosition = 400 + index * 150; // Adjusted for new layout
        scrollViewRef.current.scrollTo({ y: optionPosition, animated: true });
      }
    }
  };

  // Add to cart handler with support for inner options
  const handleAddToCart = async () => {
    // Check if business is closed
    if (checkBusinessHours(businessHours)) {
      return; // Don't proceed if business is closed
    }

    const invalidOptions = validateOptions();

    if (invalidOptions.length > 0) {
      // Set error message
      setErrorMessage(
        `Please select required options: ${invalidOptions
          .map((opt) => opt.name)
          .join(", ")}`
      );

      // Highlight the invalid options
      setHighlightedOptions(invalidOptions.map((opt) => opt.id));

      // Scroll to the first invalid option
      if (invalidOptions.length > 0) {
        scrollToOption(invalidOptions[0].id);
      }

      return;
    }

    // Format selected options for API
    const formattedOptions = {};

    item.options.forEach((optionSet) => {
      const selectedItems = selectedOptions[optionSet.id]?.items || {};
      const optionItems = [];

      optionSet.items.forEach((optItem) => {
        if (selectedItems[optItem.id]) {
          const innerOptionsFormatted = {};

          // Process inner options if they exist in 'items' array (new structure)
          if (
            optItem.items &&
            Array.isArray(optItem.items) &&
            optItem.items.length > 0
          ) {
            optItem.items.forEach((innerOptionSet) => {
              const innerKey = `${optionSet.id}_${optItem.id}_${innerOptionSet.id}`;
              const selectedInnerItems = selectedOptions[innerKey]?.items || {};
              const innerItems = [];

              innerOptionSet.items.forEach((innerItem) => {
                if (selectedInnerItems[innerItem.id]) {
                  // Check for nested inner options in 'items' array
                  const nestedInnerOptionsFormatted = {};

                  if (
                    innerItem.items &&
                    Array.isArray(innerItem.items) &&
                    innerItem.items.length > 0
                  ) {
                    innerItem.items.forEach((nestedInnerOptionSet) => {
                      const nestedInnerKey = `${optionSet.id}_${optItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                      const selectedNestedInnerItems =
                        selectedOptions[nestedInnerKey]?.items || {};
                      const nestedInnerItems = [];

                      nestedInnerOptionSet.items.forEach((nestedInnerItem) => {
                        if (selectedNestedInnerItems[nestedInnerItem.id]) {
                          nestedInnerItems.push({
                            name: nestedInnerItem.name,
                            price: nestedInnerItem.price,
                            quantity:
                              selectedNestedInnerItems[nestedInnerItem.id],
                          });
                        }
                      });

                      if (nestedInnerItems.length > 0) {
                        nestedInnerOptionsFormatted[nestedInnerOptionSet.name] =
                          nestedInnerItems;
                      }
                    });
                  }

                  // Also check for nested inner options in 'inner_options' array (old structure)
                  if (
                    innerItem.inner_options &&
                    innerItem.inner_options.length > 0
                  ) {
                    innerItem.inner_options.forEach((nestedInnerOptionSet) => {
                      const nestedInnerKey = `${optionSet.id}_${optItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                      const selectedNestedInnerItems =
                        selectedOptions[nestedInnerKey]?.items || {};
                      const nestedInnerItems = [];

                      nestedInnerOptionSet.items.forEach((nestedInnerItem) => {
                        if (selectedNestedInnerItems[nestedInnerItem.id]) {
                          nestedInnerItems.push({
                            name: nestedInnerItem.name,
                            price: nestedInnerItem.price,
                            quantity:
                              selectedNestedInnerItems[nestedInnerItem.id],
                          });
                        }
                      });

                      if (nestedInnerItems.length > 0) {
                        nestedInnerOptionsFormatted[nestedInnerOptionSet.name] =
                          nestedInnerItems;
                      }
                    });
                  }

                  innerItems.push({
                    name: innerItem.name,
                    price: innerItem.price,
                    quantity: selectedInnerItems[innerItem.id],
                    inner_options: nestedInnerOptionsFormatted,
                  });
                }
              });

              if (innerItems.length > 0) {
                innerOptionsFormatted[innerOptionSet.name] = innerItems;
              }
            });
          }

          // Also process inner options if they exist in 'inner_options' array (old structure)
          if (optItem.inner_options && optItem.inner_options.length > 0) {
            optItem.inner_options.forEach((innerOptionSet) => {
              const innerKey = `${optionSet.id}_${optItem.id}_${innerOptionSet.id}`;
              const selectedInnerItems = selectedOptions[innerKey]?.items || {};
              const innerItems = [];

              innerOptionSet.items.forEach((innerItem) => {
                if (selectedInnerItems[innerItem.id]) {
                  // Check for nested inner options
                  const nestedInnerOptionsFormatted = {};

                  if (
                    innerItem.inner_options &&
                    innerItem.inner_options.length > 0
                  ) {
                    innerItem.inner_options.forEach((nestedInnerOptionSet) => {
                      const nestedInnerKey = `${optionSet.id}_${optItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;
                      const selectedNestedInnerItems =
                        selectedOptions[nestedInnerKey]?.items || {};
                      const nestedInnerItems = [];

                      nestedInnerOptionSet.items.forEach((nestedInnerItem) => {
                        if (selectedNestedInnerItems[nestedInnerItem.id]) {
                          nestedInnerItems.push({
                            name: nestedInnerItem.name,
                            price: nestedInnerItem.price,
                            quantity:
                              selectedNestedInnerItems[nestedInnerItem.id],
                          });
                        }
                      });

                      if (nestedInnerItems.length > 0) {
                        nestedInnerOptionsFormatted[nestedInnerOptionSet.name] =
                          nestedInnerItems;
                      }
                    });
                  }

                  innerItems.push({
                    name: innerItem.name,
                    price: innerItem.price,
                    quantity: selectedInnerItems[innerItem.id],
                    inner_options: nestedInnerOptionsFormatted,
                  });
                }
              });

              if (innerItems.length > 0) {
                innerOptionsFormatted[innerOptionSet.name] = innerItems;
              }
            });
          }

          optionItems.push({
            name: optItem.name,
            price: optItem.price,
            quantity: selectedItems[optItem.id],
            inner_options: innerOptionsFormatted,
          });
        }
      });

      if (optionItems.length > 0) {
        formattedOptions[optionSet.name] = optionItems;
      }
    });

    const result = await addToCart(
      item,
      "add",
      "new",
      JSON.stringify(formattedOptions),
      quantity
    );

    if (!result.success) {
      Alert.alert("Error", result.message);
    } else {
      showToastMessage("Item added successfully", "success");
      navigation.goBack();
    }
  };

  // Calculate total price including option sets, inner option sets, and nested inner option sets
  const calculateTotalPrice = () => {
    let total = item.price * quantity;

    // Add option prices
    Object.entries(selectedOptions).forEach(([optionId, optionData]) => {
      const parts = optionId.split("_");

      // Regular parent option sets (no underscore in ID)
      if (parts.length === 1) {
        const optionSet = item.options.find((opt) => opt.id === optionId);
        if (!optionSet) return;

        Object.entries(optionData.items).forEach(([itemId, itemQty]) => {
          const optionItem = optionSet.items.find((item) => item.id === itemId);
          if (optionItem) {
            total += Number(optionItem.price) * quantity;
          }
        });
      }
      // Inner option sets (3 parts in ID: parentOptionId_parentItemId_innerOptionId)
      else if (parts.length === 3) {
        const [parentOptionId, parentItemId, innerOptionId] = parts;
        const parentSelected =
          selectedOptions[parentOptionId]?.items?.[parentItemId];

        if (parentSelected) {
          const parentOptionSet = item.options.find(
            (opt) => opt.id === parentOptionId
          );
          if (!parentOptionSet) return;

          const parentItem = parentOptionSet.items.find(
            (item) => item.id === parentItemId
          );
          if (!parentItem) return;

          // Check for inner option sets in both 'items' and 'inner_options' arrays
          let innerOptionSet;

          // First check in 'items' array (new structure)
          if (parentItem.items && Array.isArray(parentItem.items)) {
            innerOptionSet = parentItem.items.find(
              (opt) => opt.id === innerOptionId
            );
          }

          // If not found, check in 'inner_options' array (old structure)
          if (!innerOptionSet && parentItem.inner_options) {
            innerOptionSet = parentItem.inner_options.find(
              (opt) => opt.id === innerOptionId
            );
          }

          if (!innerOptionSet) return;

          Object.entries(optionData.items).forEach(
            ([innerItemId, innerItemQty]) => {
              const innerItem = innerOptionSet.items.find(
                (item) => item.id === innerItemId
              );
              if (innerItem) {
                total += Number(innerItem.price) * quantity;
              }
            }
          );
        }
      }
      // Nested inner option sets (5 parts in ID: parentOptionId_parentItemId_innerOptionId_innerItemId_nestedInnerOptionId)
      else if (parts.length === 5) {
        const [
          parentOptionId,
          parentItemId,
          innerOptionId,
          innerItemId,
          nestedInnerOptionId,
        ] = parts;

        // Check if parent option is selected
        const parentSelected =
          selectedOptions[parentOptionId]?.items?.[parentItemId];
        if (!parentSelected) return;

        // Check if inner option is selected
        const innerOptionKey = `${parentOptionId}_${parentItemId}_${innerOptionId}`;
        const innerSelected =
          selectedOptions[innerOptionKey]?.items?.[innerItemId];
        if (!innerSelected) return;

        // Find the nested inner option set
        const parentOptionSet = item.options.find(
          (opt) => opt.id === parentOptionId
        );
        if (!parentOptionSet) return;

        const parentItem = parentOptionSet.items.find(
          (item) => item.id === parentItemId
        );
        if (!parentItem) return;

        // Check for inner option sets in both 'items' and 'inner_options' arrays
        let innerOptionSet;

        // First check in 'items' array (new structure)
        if (parentItem.items && Array.isArray(parentItem.items)) {
          innerOptionSet = parentItem.items.find(
            (opt) => opt.id === innerOptionId
          );
        }

        // If not found, check in 'inner_options' array (old structure)
        if (!innerOptionSet && parentItem.inner_options) {
          innerOptionSet = parentItem.inner_options.find(
            (opt) => opt.id === innerOptionId
          );
        }

        if (!innerOptionSet) return;

        const innerItem = innerOptionSet.items.find(
          (item) => item.id === innerItemId
        );
        if (!innerItem) return;

        // Check for nested inner option sets in both 'items' and 'inner_options' arrays
        let nestedInnerOptionSet;

        // First check in 'items' array (new structure)
        if (innerItem.items && Array.isArray(innerItem.items)) {
          nestedInnerOptionSet = innerItem.items.find(
            (opt) => opt.id === nestedInnerOptionId
          );
        }

        // If not found, check in 'inner_options' array (old structure)
        if (!nestedInnerOptionSet && innerItem.inner_options) {
          nestedInnerOptionSet = innerItem.inner_options.find(
            (opt) => opt.id === nestedInnerOptionId
          );
        }

        if (!nestedInnerOptionSet) return;

        // Add prices for selected nested inner options
        Object.entries(optionData.items).forEach(
          ([nestedItemId, nestedItemQty]) => {
            const nestedItem = nestedInnerOptionSet.items.find(
              (item) => item.id === nestedItemId
            );
            if (nestedItem) {
              total += Number(nestedItem.price) * quantity;
            }
          }
        );
      }
    });

    // Apply discount if available
    if (item.discount && item.discount > 0) {
      total = total - total * (item.discount / 100);
    }

    return total;
  };

  // Strip HTML tags from description
  const stripHtmlTags = (html) => {
    if (!html) return "";
    return html.replace(/(<([^>]+)>)/gi, "");
  };

  if (!item) return null;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Full-screen ScrollView */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
      >
        {/* Product Image with Back Button and Cart Button Overlay */}
        <View style={styles.imageContainer}>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => setIsImageViewerVisible(true)}
          >
            <Image
              source={{ uri: item.image }}
              style={styles.productImage}
              resizeMode="cover"
            />
          </TouchableOpacity>

          {/* Back button */}
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.8}
          >
            <MaterialIcons name="arrow-back" size={24} color="#000000" />
          </TouchableOpacity>

          {/* Cart button - only show when cart is not empty */}
          {cart?.items && cart.items.length > 0 && (
            <TouchableOpacity
              style={styles.cartButton}
              onPress={handleCartIconClick}
              activeOpacity={0.8}
            >
              <MaterialIcons name="shopping-cart" size={24} color="#000000" />
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{cart.items.length}</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Product Details */}
        <View style={styles.productDetails}>
          <Text style={styles.productName}>{item.name}</Text>

          {item.desc ? (
            <>
              <Text
                style={styles.productDescription}
                numberOfLines={isDescriptionExpanded ? undefined : 2}
                ellipsizeMode="tail"
              >
                {stripHtmlTags(item.desc)}
              </Text>

              {/* Only show Read more/less if description is long enough */}
              {item.desc.length > 80 && (
                <TouchableOpacity
                  onPress={() =>
                    setIsDescriptionExpanded(!isDescriptionExpanded)
                  }
                >
                  <Text style={styles.readMoreLink}>
                    {isDescriptionExpanded ? "Read less" : "Read more"}
                  </Text>
                </TouchableOpacity>
              )}
            </>
          ) : null}
        </View>

        {/* Option Sets */}
        {item.options &&
          item.options.map((optionSet) => (
            <View
              key={optionSet.id}
              style={[
                styles.optionSetContainer,
                highlightedOptions.includes(optionSet.id) &&
                  styles.highlightedOptionSet,
              ]}
            >
              <View style={styles.optionSetHeader}>
                <Text style={styles.optionSetTitle}>{optionSet.name}</Text>

                <View style={styles.optionSetRequirement}>
                  <Text style={styles.chooseText}>
                    Choose{" "}
                    {optionSet.min_quantity === "1"
                      ? "any 1"
                      : `${optionSet.min_quantity}`}
                  </Text>

                  {optionSet.min_quantity !== "0" && (
                    <Text
                      style={[
                        styles.requiredText,
                        isOptionSetRequirementMet(optionSet.id)
                          ? styles.requiredTextComplete
                          : {},
                      ]}
                    >
                      Required
                    </Text>
                  )}
                </View>
              </View>

              <OptionSetRenderer
                optionSet={optionSet}
                selectedOptions={selectedOptions}
                isExpanded={expandedOptions[optionSet.id]}
                onToggle={() => toggleOption(optionSet.id)}
                onSelect={handleOptionSelect}
                renderInnerOptionSets={(optionItem) => {
                  // Check for inner option sets in both 'items' and 'inner_options' arrays
                  // First check for inner option sets in 'items' array (new structure)
                  if (
                    optionItem.items &&
                    Array.isArray(optionItem.items) &&
                    optionItem.items.length > 0
                  ) {
                    return (
                      <View>
                        {optionItem.items.map((innerOptionSet) => {
                          const innerOptionId = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}`;

                          return (
                            <View
                              key={innerOptionId}
                              style={styles.innerOptionSetContainer}
                            >
                              <View style={styles.optionSetHeader}>
                                <Text style={styles.innerOptionSetTitle}>
                                  {innerOptionSet.name}
                                </Text>

                                <View style={styles.optionSetRequirement}>
                                  <Text style={styles.chooseText}>
                                    Choose{" "}
                                    {innerOptionSet.min_quantity === "1"
                                      ? "any 1"
                                      : `${innerOptionSet.min_quantity}`}
                                  </Text>

                                  {innerOptionSet.min_quantity !== "0" && (
                                    <Text
                                      style={[
                                        styles.requiredText,
                                        isOptionSetRequirementMet(innerOptionId)
                                          ? styles.requiredTextComplete
                                          : {},
                                      ]}
                                    >
                                      Required
                                    </Text>
                                  )}
                                </View>
                              </View>

                              <OptionSetRenderer
                                optionSet={{
                                  ...innerOptionSet,
                                  id: innerOptionId,
                                }}
                                selectedOptions={selectedOptions}
                                isExpanded={expandedOptions[innerOptionId]}
                                onToggle={() => toggleOption(innerOptionId)}
                                onSelect={(_, itemId) =>
                                  handleOptionSelect(
                                    innerOptionId,
                                    itemId,
                                    true,
                                    {
                                      parentOptionId: optionSet.id,
                                      parentItemId: optionItem.id,
                                    }
                                  )
                                }
                                renderInnerOptionSets={(innerItem) => {
                                  // Check for nested inner option sets in 'items' array
                                  if (
                                    innerItem.items &&
                                    Array.isArray(innerItem.items) &&
                                    innerItem.items.length > 0
                                  ) {
                                    return (
                                      <View>
                                        {innerItem.items.map(
                                          (nestedInnerOptionSet) => {
                                            const nestedInnerOptionId = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;

                                            return (
                                              <View
                                                key={nestedInnerOptionId}
                                                style={
                                                  styles.nestedInnerOptionSetContainer
                                                }
                                              >
                                                <View
                                                  style={styles.optionSetHeader}
                                                >
                                                  <Text
                                                    style={
                                                      styles.innerOptionSetTitle
                                                    }
                                                  >
                                                    {nestedInnerOptionSet.name}
                                                  </Text>

                                                  <View
                                                    style={
                                                      styles.optionSetRequirement
                                                    }
                                                  >
                                                    <Text
                                                      style={styles.chooseText}
                                                    >
                                                      Choose{" "}
                                                      {nestedInnerOptionSet.min_quantity ===
                                                      "1"
                                                        ? "any 1"
                                                        : `${nestedInnerOptionSet.min_quantity}`}
                                                    </Text>

                                                    {nestedInnerOptionSet.min_quantity !==
                                                      "0" && (
                                                      <Text
                                                        style={[
                                                          styles.requiredText,
                                                          isOptionSetRequirementMet(
                                                            nestedInnerOptionId
                                                          )
                                                            ? styles.requiredTextComplete
                                                            : {},
                                                        ]}
                                                      >
                                                        Required
                                                      </Text>
                                                    )}
                                                  </View>
                                                </View>

                                                <OptionSetRenderer
                                                  optionSet={{
                                                    ...nestedInnerOptionSet,
                                                    id: nestedInnerOptionId,
                                                  }}
                                                  selectedOptions={
                                                    selectedOptions
                                                  }
                                                  isExpanded={
                                                    expandedOptions[
                                                      nestedInnerOptionId
                                                    ]
                                                  }
                                                  onToggle={() =>
                                                    toggleOption(
                                                      nestedInnerOptionId
                                                    )
                                                  }
                                                  onSelect={(_, nestedItemId) =>
                                                    handleOptionSelect(
                                                      nestedInnerOptionId,
                                                      nestedItemId,
                                                      true,
                                                      {
                                                        parentOptionId:
                                                          innerOptionId,
                                                        parentItemId:
                                                          innerItem.id,
                                                      }
                                                    )
                                                  }
                                                />
                                              </View>
                                            );
                                          }
                                        )}
                                      </View>
                                    );
                                  }
                                  return null;
                                }}
                              />
                            </View>
                          );
                        })}
                      </View>
                    );
                  }

                  // Also check for inner option sets in 'inner_options' array (old structure)
                  if (
                    optionItem.inner_options &&
                    optionItem.inner_options.length > 0
                  ) {
                    return (
                      <View>
                        {optionItem.inner_options.map((innerOptionSet) => {
                          const innerOptionId = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}`;

                          return (
                            <View
                              key={innerOptionId}
                              style={styles.innerOptionSetContainer}
                            >
                              <View style={styles.optionSetHeader}>
                                <Text style={styles.innerOptionSetTitle}>
                                  {innerOptionSet.name}
                                </Text>

                                <View style={styles.optionSetRequirement}>
                                  <Text style={styles.chooseText}>
                                    Choose{" "}
                                    {innerOptionSet.min_quantity === "1"
                                      ? "any 1"
                                      : `${innerOptionSet.min_quantity}`}
                                  </Text>

                                  {innerOptionSet.min_quantity !== "0" && (
                                    <Text
                                      style={[
                                        styles.requiredText,
                                        isOptionSetRequirementMet(innerOptionId)
                                          ? styles.requiredTextComplete
                                          : {},
                                      ]}
                                    >
                                      Required
                                    </Text>
                                  )}
                                </View>
                              </View>

                              <OptionSetRenderer
                                optionSet={{
                                  ...innerOptionSet,
                                  id: innerOptionId,
                                }}
                                selectedOptions={selectedOptions}
                                isExpanded={expandedOptions[innerOptionId]}
                                onToggle={() => toggleOption(innerOptionId)}
                                onSelect={(_, itemId) =>
                                  handleOptionSelect(
                                    innerOptionId,
                                    itemId,
                                    true,
                                    {
                                      parentOptionId: optionSet.id,
                                      parentItemId: optionItem.id,
                                    }
                                  )
                                }
                                renderInnerOptionSets={(innerItem) => {
                                  if (
                                    innerItem.inner_options &&
                                    innerItem.inner_options.length > 0
                                  ) {
                                    return (
                                      <View>
                                        {innerItem.inner_options.map(
                                          (nestedInnerOptionSet) => {
                                            const nestedInnerOptionId = `${optionSet.id}_${optionItem.id}_${innerOptionSet.id}_${innerItem.id}_${nestedInnerOptionSet.id}`;

                                            return (
                                              <View
                                                key={nestedInnerOptionId}
                                                style={
                                                  styles.nestedInnerOptionSetContainer
                                                }
                                              >
                                                <View
                                                  style={styles.optionSetHeader}
                                                >
                                                  <Text
                                                    style={
                                                      styles.innerOptionSetTitle
                                                    }
                                                  >
                                                    {nestedInnerOptionSet.name}
                                                  </Text>

                                                  <View
                                                    style={
                                                      styles.optionSetRequirement
                                                    }
                                                  >
                                                    <Text
                                                      style={styles.chooseText}
                                                    >
                                                      Choose{" "}
                                                      {nestedInnerOptionSet.min_quantity ===
                                                      "1"
                                                        ? "any 1"
                                                        : `${nestedInnerOptionSet.min_quantity}`}
                                                    </Text>

                                                    {nestedInnerOptionSet.min_quantity !==
                                                      "0" && (
                                                      <Text
                                                        style={[
                                                          styles.requiredText,
                                                          isOptionSetRequirementMet(
                                                            nestedInnerOptionId
                                                          )
                                                            ? styles.requiredTextComplete
                                                            : {},
                                                        ]}
                                                      >
                                                        Required
                                                      </Text>
                                                    )}
                                                  </View>
                                                </View>

                                                <OptionSetRenderer
                                                  optionSet={{
                                                    ...nestedInnerOptionSet,
                                                    id: nestedInnerOptionId,
                                                  }}
                                                  selectedOptions={
                                                    selectedOptions
                                                  }
                                                  isExpanded={
                                                    expandedOptions[
                                                      nestedInnerOptionId
                                                    ]
                                                  }
                                                  onToggle={() =>
                                                    toggleOption(
                                                      nestedInnerOptionId
                                                    )
                                                  }
                                                  onSelect={(_, nestedItemId) =>
                                                    handleOptionSelect(
                                                      nestedInnerOptionId,
                                                      nestedItemId,
                                                      true,
                                                      {
                                                        parentOptionId:
                                                          innerOptionId,
                                                        parentItemId:
                                                          innerItem.id,
                                                      }
                                                    )
                                                  }
                                                />
                                              </View>
                                            );
                                          }
                                        )}
                                      </View>
                                    );
                                  }
                                  return null;
                                }}
                              />
                            </View>
                          );
                        })}
                      </View>
                    );
                  }

                  return null;
                }}
              />
            </View>
          ))}

        {/* Add spacing at the bottom to ensure content isn't hidden behind the bottom bar */}
        <View style={{ height: 100 }} />
      </ScrollView>

      {/* Error Message */}
      {errorMessage !== "" && (
        <Animated.View style={[styles.errorContainer, { opacity: fadeAnim }]}>
          <MaterialIcons name="error-outline" size={20} color="#FFFFFF" />
          <Text style={styles.errorText}>{errorMessage}</Text>
        </Animated.View>
      )}

      {/* Cart Modal */}
      <CartScreen
        visible={isCartVisible}
        onClose={handleCartClosed}
        onProceedToCheckout={() => navigation.navigate("Checkout")}
      />

      {/* Fullscreen Image Viewer Modal */}
      <Modal
        visible={isImageViewerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsImageViewerVisible(false)}
      >
        <View style={styles.imageViewerContainer}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setIsImageViewerVisible(false)}
          >
            <MaterialIcons name="close" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <ImageZoom
            uri={item.image}
            minScale={0.5}
            maxScale={3}
            isPinchEnabled={true}
            isPanEnabled={true}
            isDoubleTapEnabled={true}
            doubleTapScale={2}
            style={styles.fullscreenImage}
          />
        </View>
      </Modal>

      {/* Bottom Bar with Quantity and Add to Cart */}
      <View style={styles.bottomBar}>
        <View style={styles.bottomBarContent}>
          {/* Quantity Selector */}
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={styles.quantityCircleButton}
              onPress={() => updateQuantity(-1)}
              disabled={quantity <= 1}
            >
              <MaterialIcons name="remove" size={24} color="#000000" />
            </TouchableOpacity>

            <Text style={styles.quantityText}>{quantity}</Text>

            <TouchableOpacity
              style={[styles.quantityCircleButton, styles.quantityPlusButton]}
              onPress={() => updateQuantity(1)}
            >
              <MaterialIcons name="add" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {/* Price */}
          <View style={styles.priceContainer}>
            {item.discount && item.discount > 0 ? (
              <View style={styles.discountPriceRow}>
                <Text style={styles.originalPriceText}>
                  {formatPrice(
                    calculateTotalPrice() / (1 - item.discount / 100),
                    currency
                  )}
                </Text>
                <Text style={styles.discountedPriceText}>
                  {formatPrice(calculateTotalPrice(), currency)}
                </Text>
              </View>
            ) : (
              <Text style={styles.priceText}>
                {formatPrice(calculateTotalPrice(), currency)}
              </Text>
            )}
          </View>
        </View>

        {/* Add to Cart Button */}
        <TouchableOpacity
          style={styles.addToCartButton}
          onPress={handleAddToCart}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.addToCartText}>Add to cart</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  imageContainer: {
    width: "100%",
    height: width * 0.8, // Square image
    position: "relative",
  },
  productImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#F8F8F8",
  },
  backButton: {
    position: "absolute",
    top: Platform.OS === "android" ? StatusBar.currentHeight + 16 : 16,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  cartButton: {
    position: "absolute",
    top: Platform.OS === "android" ? StatusBar.currentHeight + 16 : 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  cartBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#FF3B30",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  cartBadgeText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  // Fullscreen image viewer styles
  imageViewerContainer: {
    flex: 1,
    backgroundColor: "#000000",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: 40,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 20,
  },
  fullscreenImage: {
    width: width,
    height: height,
  },
  // This style is now handled in the headerButtonsContainer
  errorContainer: {
    position: "absolute",
    top: width * 0.8 + 10, // Position just below the image
    left: 16,
    right: 16,
    backgroundColor: "#FF3B30",
    borderRadius: 8,
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
    zIndex: 100,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  errorText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
    flex: 1,
  },
  productDetails: {
    padding: 16,
  },
  productName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
  },
  productDescription: {
    fontSize: 16,
    color: "#555555",
    lineHeight: 22,
  },
  readMoreLink: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "500",
    marginTop: 4,
  },
  productPriceContainer: {
    marginTop: 12,
  },
  productPriceRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  productPrice: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#000000",
  },
  productOriginalPrice: {
    fontSize: 18,
    fontWeight: "500",
    color: "#999999",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  productDiscountedPrice: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#000000",
  },
  optionSetContainer: {
    marginBottom: 24,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
  },
  highlightedOptionSet: {
    backgroundColor: "#FFF5F5",
    borderWidth: 1,
    borderColor: "#FF3B30",
  },
  optionSetHeader: {
    marginBottom: 12,
  },
  optionSetTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 4,
  },
  innerOptionSetTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 2,
  },
  optionSetRequirement: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  chooseText: {
    fontSize: 14,
    color: "#555555",
  },
  requiredText: {
    fontSize: 14,
    color: "#FF3B30",
    fontWeight: "500",
  },
  requiredTextComplete: {
    color: "#4CAF50", // Green color when requirement is met
  },
  innerOptionSetContainer: {
    marginTop: 0,
    marginBottom: 8,
    paddingLeft: 32, // Indent inner options
  },
  nestedInnerOptionSetContainer: {
    marginTop: 0,
    marginBottom: 8,
    paddingLeft: 32, // Indent nested inner options
  },
  bottomBar: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
  },
  bottomBarContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityCircleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
  },
  quantityPlusButton: {
    backgroundColor: "#000000",
  },
  quantityText: {
    fontSize: 18,
    fontWeight: "bold",
    marginHorizontal: 16,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
  },
  discountPriceContainer: {
    alignItems: "flex-end",
  },
  discountPriceRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  originalPriceText: {
    fontSize: 18,
    fontWeight: "500",
    color: "#999999",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  discountedPriceText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
  },
  addToCartButton: {
    backgroundColor: "#000000",
    paddingVertical: 16,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
  },
  addToCartText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  toast: {
    position: "absolute",
    bottom: 100,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    zIndex: 100,
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 0.9)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 59, 48, 0.9)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default ItemDetailScreen;
