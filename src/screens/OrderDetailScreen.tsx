"use client";
import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { useTheme } from "../theme/ThemeProvider";
import { API_ENDPOINTS, BUSINESS_ID } from "../config";
import { formatPrice } from "../utils/cartUtils";

const OrderDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const [orderDetails, setOrderDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const { orderId } = route.params || {};
  const { userData, isLoggedIn } = useSelector((state) => state.auth);
  // Get currency from Redux store
  const currency = useSelector((state) => state.order.currency);

  // Set header options
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: "Order Detail",
      headerStyle: {
        backgroundColor: "#FFFFFF",
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: "#000000",
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginLeft: 16 }}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      navigation.replace("Login");
    }
  }, [isLoggedIn, navigation]);

  // Fetch order details
  useEffect(() => {
    if (isLoggedIn && userData?.user_email && orderId) {
      fetchOrderDetails();
    }
  }, [isLoggedIn, userData, orderId]);

  const fetchOrderDetails = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Create form data for the request
      const formData = new FormData();
      formData.append("user_email", userData.user_email);
      formData.append("eatout_id", BUSINESS_ID);

      const response = await fetch(API_ENDPOINTS.ORDER_GET, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === "1" && data.result) {
        // Find the specific order
        const order = data.result.find((order) => order.order_id === orderId);
        if (order) {
          setOrderDetails(order);
        } else {
          setError("Order not found");
        }
      } else {
        setError(data.message || "Failed to fetch order details");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("Failed to load order details. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
        year: "numeric",
      });
    } catch (e) {
      return dateString;
    }
  };

  // Format time
  const formatTime = (dateString) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      let hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? "PM" : "AM";
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      const formattedMinutes = minutes < 10 ? "0" + minutes : minutes;
      return `${hours}:${formattedMinutes}${ampm}`;
    } catch (e) {
      return "";
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Delivered":
        return "#4CAF50";
      case "Confirmed":
        return "#4CAF50"; // Green for confirmed
      case "Cancelled":
        return "#FF3B30";
      case "Pending":
        return "#FF9800"; // Orange for pending
      default:
        return "#000000";
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  if (error || !orderDetails) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={64} color="#FF3B30" />
          <Text style={styles.errorTitle}>Something Went Wrong</Text>
          <Text style={styles.errorText}>{error || "Order not found"}</Text>
          <TouchableOpacity
            style={styles.tryAgainButton}
            onPress={fetchOrderDetails}
          >
            <Text style={styles.tryAgainText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Calculate subtotal (sum of all items)
  const subtotal =
    orderDetails.order_detail?.reduce(
      (sum, item) => sum + (Number.parseFloat(item.total) || 0),
      0
    ) || 0;

  // Get discount, tax, and delivery charges
  const discount = Number.parseFloat(orderDetails.discount_value) || 0;
  const tax = Number.parseFloat(orderDetails.tax_value) || 0;
  const deliveryCharges = Number.parseFloat(orderDetails.delivery_charges) || 0;

  // Grand total
  const grandTotal = Number.parseFloat(orderDetails.grand_total) || 0;

  // Update the return statement in the component to modify the layout
  // Replace the existing return statement with this updated version that:
  // 1. Shows name and price in a row
  // 2. Uses formatPrice
  // 3. Moves Grand Total to the bottom
  // 4. Shows option-set details

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.container}>
        <ScrollView style={styles.scrollView}>
          {/* Order Header */}
          <View style={styles.orderHeader}>
            <Text style={styles.orderId}>#{orderDetails.order_id}</Text>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(orderDetails.status) },
              ]}
            >
              <Text style={styles.statusText}>{orderDetails.status}</Text>
            </View>
          </View>

          {/* Order Info */}
          <View style={styles.orderNamePriceRow}>
            <Text style={styles.orderName}>
              {orderDetails.name || "Test Order"}
            </Text>
            <Text style={styles.priceText}>
              {formatPrice(orderDetails.grand_total, currency || "Rs")}
            </Text>
          </View>

          <View style={styles.locationContainer}>
            <MaterialIcons name="location-on" size={20} color="#000000" />
            <Text style={styles.locationText}>
              {orderDetails.user_area || "Model Town"}
            </Text>
          </View>

          <View style={styles.dateContainer}>
            <MaterialIcons name="access-time" size={20} color="#000000" />
            <Text style={styles.dateText}>{formatDate(orderDetails.date)}</Text>
            <Text style={styles.timeText}>{formatTime(orderDetails.date)}</Text>
          </View>

          <View style={styles.divider} />

          {/* Order Items */}
          <View style={styles.orderItemsContainer}>
            <View style={styles.orderItemsHeader}>
              <Text style={styles.orderItemsTitle}>
                Order Details ({orderDetails.order_detail?.length || 0}{" "}
                {orderDetails.order_detail?.length === 1 ? "Item" : "Items"})
              </Text>
              <Text style={styles.priceColumnTitle}>Price</Text>
            </View>

            {orderDetails.order_detail?.map((item, index) => {
              // Parse options if they exist
              let options = [];
              try {
                if (item.options && item.options !== "{}") {
                  const parsedOptions = JSON.parse(item.options);
                  // Convert the options object to an array of option sets
                  options = Object.entries(parsedOptions).map(
                    ([name, items]) => ({
                      name,
                      items: Array.isArray(items) ? items : [items],
                    })
                  );
                }
              } catch (e) {
                console.error("Error parsing options:", e);
              }

              return (
                <View key={index} style={styles.orderItem}>
                  <View style={styles.orderItemMainRow}>
                    <View style={styles.orderItemInfo}>
                      <Text style={styles.orderItemQuantity}>
                        {item.quantity}x
                      </Text>
                      <Text style={styles.orderItemName}>{item.item_name}</Text>
                    </View>
                    <Text style={styles.orderItemPrice}>
                      {formatPrice(item.total, currency || "Rs")}
                    </Text>
                  </View>

                  {/* Option Sets */}
                  {options.length > 0 && (
                    <View style={styles.optionSetsContainer}>
                      {options.map((optionSet, optIndex) => (
                        <View key={optIndex} style={styles.optionSetRow}>
                          <View style={styles.optionSetInfo}>
                            <Text style={styles.optionSetName}>
                              {optionSet.name}
                            </Text>
                            {optionSet.items.map((opt, i) => (
                              <Text key={i} style={styles.optionItemName}>
                                {opt.name}{" "}
                                {opt.quantity > 1 ? `x${opt.quantity}` : ""}
                              </Text>
                            ))}
                          </View>
                          <View style={styles.optionSetPrices}>
                            {optionSet.items.map((opt, i) =>
                              Number(opt.price) > 0 ? (
                                <Text key={i} style={styles.optionItemPrice}>
                                  {formatPrice(
                                    opt.price * (opt.quantity || 1),
                                    currency || "Rs"
                                  )}
                                </Text>
                              ) : null
                            )}
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>

        {/* Order Summary - Fixed at bottom */}
        <View style={styles.orderSummaryContainer}>
          <TouchableOpacity
            style={styles.orderSummaryHeader}
            onPress={() => setIsExpanded(!isExpanded)}
          >
            <Text style={styles.orderSummaryTitle}>Grand Total</Text>
            <View style={styles.orderSummaryHeaderRight}>
              <Text style={styles.orderSummaryTotal}>
                {formatPrice(grandTotal, currency || "Rs")}
              </Text>
              <MaterialIcons
                name={isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                size={24}
                color="#000000"
              />
            </View>
          </TouchableOpacity>

          {isExpanded && (
            <View style={styles.orderSummaryDetails}>
              <View style={styles.orderSummaryRow}>
                <Text style={styles.orderSummaryLabel}>Subtotal</Text>
                <Text style={styles.orderSummaryValue}>
                  {formatPrice(subtotal, currency || "Rs")}
                </Text>
              </View>

              <View style={styles.orderSummaryRow}>
                <Text style={styles.orderSummaryLabel}>Discount</Text>
                <Text style={styles.orderSummaryValue}>
                  {formatPrice(discount, currency || "Rs")}
                </Text>
              </View>

              <View style={styles.orderSummaryRow}>
                <Text style={styles.orderSummaryLabel}>Tax</Text>
                <Text style={styles.orderSummaryValue}>
                  {formatPrice(tax, currency || "Rs")}
                </Text>
              </View>

              {deliveryCharges > 0 && (
                <View style={styles.orderSummaryRow}>
                  <Text style={styles.orderSummaryLabel}>Delivery Fee</Text>
                  <Text style={styles.orderSummaryValue}>
                    {formatPrice(deliveryCharges, currency || "Rs")}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </View>
    </SafeAreaView>
  );

  // Update the styles to support the new layout
  // Replace the existing styles with these updated styles
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    flex: 1,
    paddingBottom: 80, // Add padding to make room for the fixed Grand Total section
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 24,
    textAlign: "center",
  },
  tryAgainButton: {
    backgroundColor: "#000000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  tryAgainText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    paddingTop: 24,
  },
  orderId: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusText: {
    fontSize: 14,
    color: "#FFFFFF",
    fontWeight: "600",
  },
  orderNamePriceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  orderName: {
    fontSize: 18,
    fontWeight: "500",
    color: "#000000",
  },
  priceText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  locationText: {
    fontSize: 16,
    color: "#000000",
    marginLeft: 8,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  dateText: {
    fontSize: 16,
    color: "#000000",
    marginLeft: 8,
    flex: 1,
  },
  timeText: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "500",
  },
  divider: {
    height: 8,
    backgroundColor: "#F5F5F5",
  },
  orderItemsContainer: {
    padding: 16,
  },
  orderItemsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  orderItemsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  priceColumnTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  orderItem: {
    marginBottom: 16,
  },
  orderItemMainRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  orderItemInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  orderItemQuantity: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
    marginRight: 8,
  },
  orderItemName: {
    fontSize: 16,
    color: "#000000",
    flex: 1,
  },
  orderItemPrice: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
  },
  optionSetsContainer: {
    marginLeft: 24,
    marginTop: 4,
  },
  optionSetRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  optionSetInfo: {
    flex: 1,
  },
  optionSetName: {
    fontSize: 14,
    color: "#666666",
    fontWeight: "500",
  },
  optionItemName: {
    fontSize: 12,
    color: "#888888",
  },
  optionSetPrices: {
    alignItems: "flex-end",
  },
  optionItemPrice: {
    fontSize: 12,
    color: "#888888",
  },
  orderSummaryContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
    padding: 16,
  },
  orderSummaryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  orderSummaryTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  orderSummaryHeaderRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  orderSummaryTotal: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginRight: 8,
  },
  orderSummaryDetails: {
    marginTop: 16,
  },
  orderSummaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  orderSummaryLabel: {
    fontSize: 16,
    color: "#000000",
  },
  orderSummaryValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
  },
});

export default OrderDetailScreen;
