"use client";
import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useTheme } from "../theme/ThemeProvider";
import { useSelector, useDispatch } from "react-redux";
import { logout, loginSuccess } from "../store/authSlice";
import { BRAND_ID, API_ENDPOINTS } from "../config";

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const { userData, isLoggedIn } = useSelector((state) => state.auth);

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Form state for editing
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
  });

  // Original data for comparison
  const [originalData, setOriginalData] = useState({
    name: "",
    email: "",
    phone: "",
  });

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      // We don't need to navigate to Login here anymore
      // The TabNavigator will handle showing LoginScreen when isLoggedIn is false
    } else {
      // Fetch the latest profile data
      fetchProfileData();
    }
  }, [isLoggedIn]);

  // Fetch profile data from API
  const fetchProfileData = async () => {
    if (!userData?.user_email) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formData = new FormData();
      formData.append("user_email", userData.user_email);
      formData.append("brand_id", BRAND_ID);

      // Make API call to get profile
      const response = await fetch(API_ENDPOINTS.GET_PROFILE, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const data = await response.json();
      console.log("Get profile response:", data);

      if (data.status === "1" && data.result) {
        // Update Redux store with the latest user data
        const updatedUserData = {
          ...userData,
          user_id: data.result.user_id,
          user_fullname: data.result.user_fullname,
          user_gender: data.result.user_gender,
          user_address: data.result.user_address,
          user_dob: data.result.user_dob,
          user_cphone: data.result.user_cphone,
          td_user_id: data.result.td_user_id,
          user_city: data.result.user_city,
        };

        dispatch(loginSuccess(updatedUserData));

        // Update form data and original data
        const newData = {
          name: data.result.user_fullname || "",
          email: data.result.user_email || "",
          phone: data.result.user_cphone || "",
        };

        setFormData(newData);
        setOriginalData(newData);
      } else {
        console.warn("Failed to get profile data:", data.message);
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Reset form data when editing mode changes
  useEffect(() => {
    if (isEditing) {
      setFormData({
        name: userData?.user_fullname || "",
        email: userData?.user_email || "",
        phone: userData?.user_cphone || "",
      });
      setOriginalData({
        name: userData?.user_fullname || "",
        email: userData?.user_email || "",
        phone: userData?.user_cphone || "",
      });
      setHasChanges(false);
    }
  }, [isEditing, userData]);

  // Check for changes when form data changes
  useEffect(() => {
    if (isEditing) {
      const changed =
        formData.name !== originalData.name ||
        formData.phone !== originalData.phone;

      setHasChanges(changed);
    }
  }, [formData, originalData, isEditing]);

  // Set up header without back button
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: "Profile",
      headerStyle: {
        backgroundColor: "#FFFFFF",
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: "#000000",
      headerLeft: null, // Remove back button in all cases
    });
  }, [navigation]);

  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formDataToSend = new FormData();
      formDataToSend.append("user_fullname", formData.name);
      formDataToSend.append("user_email", userData?.user_email || "");
      formDataToSend.append("user_cphone", formData.phone);
      formDataToSend.append("brand_id", BRAND_ID);
      formDataToSend.append("user_address", userData?.user_address || "");
      formDataToSend.append("user_dob", userData?.user_dob || "");
      formDataToSend.append("user_city", userData?.user_city || "");

      // Make API call to update profile
      const response = await fetch(API_ENDPOINTS.UPDATE_PROFILE, {
        method: "POST",
        body: formDataToSend,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const data = await response.json();
      console.log("Update profile response:", data);

      if (data.status === "1") {
        // Update Redux store with new user data
        dispatch(
          loginSuccess({
            ...userData,
            user_fullname: data.result.user_fullname,
            user_cphone: data.result.user_cphone,
            // Keep other fields from the response
            ...data.result,
          })
        );

        setIsEditing(false);
        // Show success message from API
        Alert.alert("Success", data.message || "Profile updated successfully");

        // Refresh profile data
        fetchProfileData();
      } else {
        // Show error message
        Alert.alert("Error", data.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      Alert.alert("Error", "Network error. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({
      name: userData?.user_fullname || "",
      email: userData?.user_email || "",
      phone: userData?.user_cphone || "",
    });
    setHasChanges(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      {
        text: "Logout",
        onPress: () => {
          // This will clear AsyncStorage through the authSlice
          dispatch(logout());

          // We don't need to navigate to Login here
          // The TabNavigator will handle showing LoginScreen when isLoggedIn is false
        },
      },
    ]);
  };

  const handleDeleteAccount = () => {
    navigation.navigate("DeleteAccount");
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {userData?.user_fullname?.charAt(0) || "U"}
            </Text>
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>
              {userData?.user_fullname || "User"}
            </Text>
            <Text style={styles.profileEmail}>
              {userData?.user_email || "<EMAIL>"}
            </Text>
            <Text style={styles.profileNumber}>
              {userData?.user_cphone || "No phone number"}
            </Text>
          </View>
          {!isEditing ? (
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setIsEditing(true)}
            >
              <MaterialIcons name="edit" size={24} color="#000000" />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
            >
              <MaterialIcons name="close" size={24} color="#FF3B30" />
            </TouchableOpacity>
          )}
        </View>

        {/* Profile Form */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Full Name</Text>
            {isEditing ? (
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(text) => handleInputChange("name", text)}
                placeholder="Enter your full name"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_fullname || "Not provided"}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Email Address</Text>
            {isEditing ? (
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={formData.email}
                editable={false}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_email || "Not provided"}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Phone Number</Text>
            {isEditing ? (
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={(text) => handleInputChange("phone", text)}
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_cphone || "Not provided"}
              </Text>
            )}
          </View>

          {isEditing && (
            <TouchableOpacity
              style={[
                styles.saveButton,
                !hasChanges && styles.disabledSaveButton,
              ]}
              onPress={handleSave}
              disabled={isSaving || !hasChanges}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.saveButtonText}>Update</Text>
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Logout Button - Only show when not editing */}
        {!isEditing && (
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        )}

        {/* Request Account Deletion Button - Only show when not editing */}
        {!isEditing && (
          <TouchableOpacity
            style={styles.deleteAccountButton}
            onPress={handleDeleteAccount}
          >
            <Text style={styles.deleteAccountButtonText}>
              Request Account Deletion
            </Text>
          </TouchableOpacity>
        )}

        {/* Bottom spacing */}
        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    padding: 16,
  },
  profileHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  avatarContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "#000000",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  avatarText: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "PlusJakartaSans-Bold",
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 4,
    fontFamily: "PlusJakartaSans-Bold",
  },
  profileEmail: {
    fontSize: 14,
    color: "#666666",
    fontFamily: "PlusJakartaSans-Regular",
  },
  editButton: {
    padding: 8,
  },
  cancelButton: {
    padding: 8,
  },
  formContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 16,
    fontFamily: "PlusJakartaSans-Bold",
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 8,
    fontFamily: "PlusJakartaSans-Regular",
  },
  value: {
    fontSize: 16,
    color: "#000000",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
    fontFamily: "PlusJakartaSans-Regular",
  },
  input: {
    fontSize: 16,
    color: "#000000",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    fontFamily: "PlusJakartaSans-Regular",
  },
  readOnlyInput: {
    backgroundColor: "#F5F5F5",
    color: "#666666",
  },
  saveButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: "center",
    marginTop: 16,
  },
  disabledSaveButton: {
    backgroundColor: "#CCCCCC",
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
    fontFamily: "PlusJakartaSans-Bold",
  },
  logoutButton: {
    backgroundColor: "#FF3B30",
    borderRadius: 100,
    paddingVertical: 14,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24,
  },
  logoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "PlusJakartaSans-Medium",
  },
  deleteAccountButton: {
    backgroundColor: "#666666",
    borderRadius: 100,
    paddingVertical: 14,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
  },
  deleteAccountButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "PlusJakartaSans-Medium",
  },
  profileNumber: {
    fontSize: 12,
    color: "#888888",
    marginTop: 2,
    fontFamily: "PlusJakartaSans-Regular",
  },
});

export default ProfileScreen;
