"use client";

import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Animated,
  Dimensions,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import DropDownPicker from "react-native-dropdown-picker";
import {
  setBanner,
  setCurrency,
  setUsername,
  setLogo,
  setAllBranches,
  setSelectedBranch,
  setAllCategories,
  setAllItems,
  setOrderType,
  setUniqueOrderId,
  setBusinessHours,
} from "../store/orderSlice";
import {
  fetchGlobalSettingsStart,
  fetchGlobalSettingsSuccess,
  fetchGlobalSettingsFailure,
} from "../store/globalSettingsSlice";
import { useTheme } from "../theme/ThemeProvider";
import { Button } from "../components/ui/BUtton";
import { Card } from "../components/ui/Card";
import { API_ENDPOINTS } from "../config"; // Import API endpoints from config

const { width, height } = Dimensions.get("window");

const SplashScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { theme, typography, spacing, borderRadius } = useTheme();

  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedBranch, setSelectedBranchState] = useState(null);
  const [branchesList, setBranchesList] = useState([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoadingMenu, setIsLoadingMenu] = useState(false);

  // Dropdown state
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState([]);

  // Redux state
  const businessId = useSelector((state) => state.order.businessId);
  const orderType = useSelector((state) => state.order.orderType);
  const { logo } = useSelector((state: any) => state.order);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Initialize order ID and fetch data
    dispatch(setUniqueOrderId());
    fetchRestaurantData();
  }, []);

  const fetchRestaurantData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${API_ENDPOINTS.RESTAURANT_DETAILS}?restaurant_id=${businessId}`
      );
      const data = await response.json();

      dispatch(setCurrency(data.result.currency));
      dispatch(setUsername(data.result.username));
      dispatch(setLogo(data.result.logo));

      fetchBanners(data.result.username, businessId);
    } catch (error) {
      console.error("Error fetching restaurant data:", error);
      setIsLoading(false);
      Alert.alert("Error", "Failed to load restaurant data. Please try again.");
    }
  };

  const fetchBanners = async (username, busId) => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.RESTAURANT_THEME}?user_name=${username}&flag=0`
      );
      const data = await response.json();

      const banners = JSON.parse(data.result.web_theme);
      const bannersListing = banners.banner;
      dispatch(setBanner(bannersListing));

      fetchBranches(busId);
    } catch (error) {
      console.error("Error fetching banner data:", error);
      setIsLoading(false);
      Alert.alert(
        "Error",
        "Failed to load restaurant details. Please try again."
      );
    }
  };

  // Fetch global settings
  const fetchGlobalSettings = async (businessId) => {
    try {
      dispatch(fetchGlobalSettingsStart());

      const response = await fetch(
        `${API_ENDPOINTS.GLOBAL_SETTINGS}?restaurant_id=${businessId}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log("Global settings response:", data);

      dispatch(fetchGlobalSettingsSuccess(data));

      return data;
    } catch (error) {
      console.error("Error fetching global settings:", error);
      dispatch(
        fetchGlobalSettingsFailure(
          error.message || "Failed to load global settings"
        )
      );
      return null;
    }
  };

  const fetchBranches = async (businessId) => {
    try {
      // Fetch global settings first
      await fetchGlobalSettings(businessId);

      const response = await fetch(
        `${API_ENDPOINTS.BRANCHES}?source=app&restaurant_id=${businessId}`
      );
      const data = await response.json();

      setIsLoading(false);
      dispatch(setAllBranches(data.result));

      // Determine available services
      const hasDelivery = data.result.some((branch) => branch.delivery === "1");
      const hasPickup = data.result.some((branch) => branch.pickup === "1");

      // Set default order type
      if (hasDelivery) {
        dispatch(setOrderType("delivery"));
      } else if (hasPickup) {
        dispatch(setOrderType("pickup"));
      }

      if (data.result.length > 0) {
        setBranchesList(data.result);

        if (data.result.length === 1) {
          // If only one branch, select it automatically
          const singleBranch = data.result[0];
          dispatch(setSelectedBranch(singleBranch));
          setSelectedBranchState(singleBranch.branch_id);

          // Fetch business hours for the selected branch
          fetchBusinessHours(businessId, singleBranch.branch_id);

          // Continue with menu fetch
          fetchMenu(singleBranch.branch_id);
        } else {
          // Show branch selection modal
          setModalVisible(true);
        }
      } else {
        Alert.alert(
          "No Branches",
          "No branches available for this restaurant."
        );
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
      setIsLoading(false);
      Alert.alert(
        "Error",
        "Failed to load restaurant branches. Please try again."
      );
    }
  };

  // Update the fetchBusinessHours function to ensure date format is region-independent
  const fetchBusinessHours = async (eatoutId, branchId) => {
    try {
      // Format current date in a region-independent way
      // Use UTC format to ensure consistency across regions
      const now = new Date();

      // Convert to Pakistan time (UTC+5)
      const pakistanTime = new Date(now.getTime() + 5 * 60 * 60 * 1000);

      const year = pakistanTime.getUTCFullYear();
      const month = String(pakistanTime.getUTCMonth() + 1).padStart(2, "0");
      const day = String(pakistanTime.getUTCDate()).padStart(2, "0");
      const hours = String(pakistanTime.getUTCHours()).padStart(2, "0");
      const minutes = String(pakistanTime.getUTCMinutes()).padStart(2, "0");
      const seconds = String(pakistanTime.getUTCSeconds()).padStart(2, "0");

      // Format: YYYY-MM-DD HH:MM:SS
      const currentDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

      console.log("Fetching business hours with params:", {
        eatout_id: eatoutId,
        date: currentDate,
        branch_id: branchId,
        localTime: new Date().toLocaleString(),
        pakistanTime: pakistanTime.toUTCString(),
      });

      // Create form data
      const formData = new FormData();
      formData.append("eatout_id", eatoutId);
      formData.append("date", currentDate);
      formData.append("branch_id", branchId);

      // Try with FormData first
      const response = await fetch(`${API_ENDPOINTS.BUSINESS_HOURS}`, {
        method: "POST",
        body: formData,
      });

      // Log response status for debugging
      console.log("Business hours API response status:", response.status);

      if (!response.ok) {
        // If first attempt fails, try with URL encoded format
        console.log("First attempt failed, trying URL encoded format...");

        const urlEncodedBody = `eatout_id=${eatoutId}&date=${encodeURIComponent(
          currentDate
        )}&branch_id=${branchId}`;

        const secondResponse = await fetch(`${API_ENDPOINTS.BUSINESS_HOURS}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: urlEncodedBody,
        });

        console.log("Second attempt status:", secondResponse.status);

        if (!secondResponse.ok) {
          throw new Error(`HTTP error! Status: ${secondResponse.status}`);
        }

        const data = await secondResponse.json();
        console.log("Business hours response:", data);

        if (data.status === "1" && data.result) {
          dispatch(setBusinessHours(data.result));
        } else {
          console.warn(
            "No business hours data available or invalid response format"
          );
        }
      } else {
        const data = await response.json();
        console.log("Business hours response:", data);

        if (data.status === "1" && data.result) {
          dispatch(setBusinessHours(data.result));
        } else {
          console.warn(
            "No business hours data available or invalid response format"
          );
        }
      }
    } catch (error) {
      console.error("Error fetching business hours:", error);
      // Log more details about the error
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      // Don't block the app flow if business hours fetch fails
    }
  };

  const fetchMenu = async (branchId) => {
    setIsLoadingMenu(true); // Show loader when fetching menu
    try {
      const response = await fetch(
        `${API_ENDPOINTS.PRODUCTS}?source=android_app&display_source=2&business_id=${businessId}&branch_id=${branchId}&menu_item=0&menu_type_id=0`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();

      dispatch(setAllCategories(data.categories));
      dispatch(setAllItems(data.items));

      // Navigate to main tab navigator
      navigation.replace("Main");
    } catch (error) {
      console.error("Error fetching menu data:", error);
      Alert.alert("Error", "Failed to load menu. Please try again.");
    } finally {
      setIsLoadingMenu(false); // Hide loader when done
    }
  };

  const handleBranchSelect = (branchId) => {
    if (branchId) {
      const selectedBranchObj = branchesList.find(
        (branch) => branch.branch_id === branchId
      );
      if (selectedBranchObj) {
        dispatch(setSelectedBranch(selectedBranchObj));

        // Fetch business hours for the selected branch
        fetchBusinessHours(businessId, branchId);

        // Continue with menu fetch
        fetchMenu(branchId);
      } else {
        Alert.alert("Error", "Selected branch not found. Please try again.");
      }
    }
  };

  // Filter branches based on selected order type
  const filteredBranches = React.useMemo(() => {
    if (orderType === "delivery") {
      return branchesList.filter((branch) => branch.delivery === "1");
    } else if (orderType === "pickup") {
      return branchesList.filter((branch) => branch.pickup === "1");
    }
    return branchesList;
  }, [branchesList, orderType]);

  // Update dropdown items when filteredBranches change
  useEffect(() => {
    const branchItems = filteredBranches.map((branch) => ({
      label: `${branch.address}, ${branch.area}, ${branch.city}`,
      value: branch.branch_id,
    }));

    setItems([{ label: "Select a branch", value: null }, ...branchItems]);
  }, [filteredBranches]);

  // Reset selectedBranch if it's not in the filtered list
  useEffect(() => {
    if (
      selectedBranch &&
      !filteredBranches.some((branch) => branch.branch_id === selectedBranch)
    ) {
      setSelectedBranchState(null);
    }
  }, [filteredBranches, selectedBranch]);

  // Handle Proceed button press
  const onProceed = () => {
    if (selectedBranch) {
      setIsLoadingMenu(true); // Show loader when proceeding
      handleBranchSelect(selectedBranch);
    } else {
      setErrorMessage("Please select a branch.");
    }
  };

  // Check if delivery and pickup are available
  const hasDelivery = React.useMemo(() => {
    return branchesList.some((branch) => branch?.delivery === "1");
  }, [branchesList]);

  const hasPickup = React.useMemo(() => {
    return branchesList.some((branch) => branch?.pickup === "1");
  }, [branchesList]);

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Global loading overlay */}
      {(isLoading || isLoadingMenu) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      )}

      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {logo ? (
          <Image
            source={{ uri: logo }}
            style={styles.splashImage}
            resizeMode="contain"
          />
        ) : (
          <Image
            source={require("../assets/splash.jpg")}
            style={styles.splashImage}
            resizeMode="contain"
          />
        )}
      </Animated.View>

      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <Card style={styles.modalContent} elevation={3}>
            <Text
              style={[
                styles.modalTitle,
                { color: theme.text, ...typography.h3 },
              ]}
            >
              Choose Your Preference
            </Text>

            <View style={styles.toggleContainer}>
              <View style={[styles.iconToggle, { borderColor: theme.border }]}>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      branchesList.some((branch) => branch.delivery === "1")
                    ) {
                      dispatch(setOrderType("delivery"));
                    } else {
                      // Show toast message for unavailable option
                      Alert.alert(
                        "Not Available",
                        "Delivery is not available for this restaurant."
                      );
                    }
                  }}
                  style={[
                    styles.iconButton,
                    orderType === "delivery"
                      ? { backgroundColor: theme.primary }
                      : { backgroundColor: theme.surface },
                    !branchesList.some((branch) => branch.delivery === "1") &&
                      styles.disabledButton,
                  ]}
                >
                  <Text
                    style={[
                      styles.iconLabel,
                      orderType === "delivery"
                        ? { color: "#FFFFFF", fontWeight: "bold" }
                        : { color: theme.textSecondary },
                      !branchesList.some((branch) => branch.delivery === "1") &&
                        styles.disabledText,
                    ]}
                  >
                    Delivery
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    if (branchesList.some((branch) => branch.pickup === "1")) {
                      dispatch(setOrderType("pickup"));
                    } else {
                      // Show toast message for unavailable option
                      Alert.alert(
                        "Not Available",
                        "Pickup is not available for this restaurant."
                      );
                    }
                  }}
                  style={[
                    styles.iconButton,
                    orderType === "pickup"
                      ? { backgroundColor: theme.primary }
                      : { backgroundColor: theme.surface },
                    !branchesList.some((branch) => branch.pickup === "1") &&
                      styles.disabledButton,
                  ]}
                >
                  <Text
                    style={[
                      styles.iconLabel,
                      orderType === "pickup"
                        ? { color: "#FFFFFF", fontWeight: "bold" }
                        : { color: theme.textSecondary },
                      !branchesList.some((branch) => branch.pickup === "1") &&
                        styles.disabledText,
                    ]}
                  >
                    Pickup
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <Text
              style={[
                styles.sectionTitle,
                { color: theme.text, ...typography.h5 },
              ]}
            >
              Select Branch
            </Text>

            <View style={styles.pickerContainer}>
              <DropDownPicker
                open={open}
                value={selectedBranch}
                items={items}
                setOpen={setOpen}
                setValue={setSelectedBranchState}
                setItems={setItems}
                placeholder="Select a branch"
                style={[
                  styles.dropdown,
                  { backgroundColor: theme.surface, borderColor: theme.border },
                ]}
                dropDownContainerStyle={[
                  styles.dropdownContainer,
                  { backgroundColor: theme.surface, borderColor: theme.border },
                ]}
                textStyle={[styles.dropdownText, { color: theme.text }]}
                placeholderStyle={[
                  styles.placeholderText,
                  { color: theme.textLight },
                ]}
                onChangeValue={(value) => {
                  setSelectedBranchState(value);
                  if (value) {
                    setErrorMessage("");
                  }
                }}
                listItemLabelStyle={[
                  styles.listItemLabel,
                  { color: theme.text },
                ]}
                selectedItemLabelStyle={[
                  styles.selectedItemLabel,
                  { color: theme.primary },
                ]}
                maxHeight={300}
                listMode="FLATLIST"
              />
            </View>

            {errorMessage !== "" && (
              <Text style={[styles.errorText, { color: theme.error }]}>
                {errorMessage}
              </Text>
            )}

            <Button
              title={isLoadingMenu ? "Loading..." : "Start My Order"}
              onPress={onProceed}
              disabled={!selectedBranch || isLoadingMenu}
              fullWidth
              size="large"
              style={styles.startButton}
            />
          </Card>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  splashImage: {
    width: width * 0.5,
    height: width * 0.5,
    resizeMode: "contain",
    marginBottom: 20,
  },
  appTitle: {
    marginBottom: 8,
  },
  appSubtitle: {
    marginBottom: 30,
    textAlign: "center",
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 20,
  },
  modalContent: {
    width: "100%",
    maxWidth: 400,
    padding: 24,
    paddingTop: 32,
    minHeight: 450,
  },
  modalTitle: {
    marginBottom: 24,
    textAlign: "center",
    fontFamily: "PlusJakartaSans-Bold",
  },
  sectionTitle: {
    marginTop: 16,
    marginBottom: 12,
    fontFamily: "PlusJakartaSans-SemiBold",
  },
  toggleContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  iconToggle: {
    flexDirection: "row",
    borderRadius: 8,
    borderWidth: 1,
    overflow: "hidden",
  },
  iconButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    width: 120,
  },
  iconLabel: {
    fontFamily: "PlusJakartaSans-Medium",
    fontSize: 14,
  },
  pickerContainer: {
    marginBottom: 16,
    zIndex: 1000,
  },
  dropdown: {
    borderRadius: 8,
    minHeight: 60,
  },
  dropdownContainer: {
    borderRadius: 8,
    maxHeight: 250,
  },
  dropdownText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  placeholderText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  listItemLabel: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 14,
  },
  selectedItemLabel: {
    fontFamily: "PlusJakartaSans-Medium",
    fontWeight: "600",
  },
  errorText: {
    fontFamily: "PlusJakartaSans-Regular",
    fontSize: 12,
    marginBottom: 16,
  },
  startButton: {
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledText: {
    color: "#999999",
  },
});

export default SplashScreen;
