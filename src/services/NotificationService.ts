import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Storage keys
const PUSH_TOKEN_KEY = "push_notification_token";
const NOTIFICATION_SETTINGS_KEY = "notification_settings";
const NOTIFICATIONS_STORAGE_KEY = "app_notifications";

// Default notification settings
export const defaultNotificationSettings = {
  orderUpdates: true,
  promotions: true,
  general: true,
};

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

/**
 * Register for push notifications and return the token
 */
export async function registerForPushNotificationsAsync() {
  let token;

  // Check if device is physical (not simulator/emulator)
  if (Device.isDevice) {
    // Check if we already have permission, otherwise request it
    const { status: existingStatus } =
      await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    // If permission not granted, return null
    if (finalStatus !== "granted") {
      console.log("Failed to get push token for push notification!");
      return null;
    }

    // Get the Expo push token
    token = (
      await Notifications.getExpoPushTokenAsync({
        projectId:
          process.env.EXPO_PROJECT_ID || "f44b6665-66b6-4fad-adb3-c6268d7d5271",
      })
    ).data;

    // Store the token in AsyncStorage
    await AsyncStorage.setItem(PUSH_TOKEN_KEY, token);

    // Log the token for testing purposes
    console.log("Push token:", token);

    // Instead of calling a non-existent API, we'll just log that we would register the token
    console.log("Would register token with backend if API existed:", token);
  } else {
    console.log("Must use physical device for Push Notifications");
  }

  // Set up notification channels for Android
  if (Platform.OS === "android") {
    Notifications.setNotificationChannelAsync("default", {
      name: "default",
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: "#FF231F7C",
    });

    // Order updates channel
    Notifications.setNotificationChannelAsync("order_updates", {
      name: "Order Updates",
      description: "Notifications about your order status",
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: "#FF231F7C",
    });

    // Promotions channel
    Notifications.setNotificationChannelAsync("promotions", {
      name: "Promotions",
      description: "Notifications about promotions and offers",
      importance: Notifications.AndroidImportance.DEFAULT,
    });

    // General channel
    Notifications.setNotificationChannelAsync("general", {
      name: "General",
      description: "General notifications",
      importance: Notifications.AndroidImportance.DEFAULT,
    });
  }

  return token;
}

/**
 * Get the stored push token
 */
export async function getStoredPushToken() {
  return await AsyncStorage.getItem(PUSH_TOKEN_KEY);
}

/**
 * Save notification settings
 */
export async function saveNotificationSettings(settings) {
  await AsyncStorage.setItem(
    NOTIFICATION_SETTINGS_KEY,
    JSON.stringify(settings)
  );
  console.log("Notification settings saved locally:", settings);

  // Log that we would update settings on backend if API existed
  console.log("Would update notification settings on backend if API existed");
}

/**
 * Get notification settings
 */
export async function getNotificationSettings() {
  const settings = await AsyncStorage.getItem(NOTIFICATION_SETTINGS_KEY);
  return settings ? JSON.parse(settings) : defaultNotificationSettings;
}

/**
 * Store a received notification
 */
export async function storeNotification(notification) {
  try {
    // Generate a unique ID if not provided
    const notificationToStore = {
      id: notification.messageId || `notification-${Date.now()}`,
      title:
        notification.title ||
        notification.request?.content?.title ||
        "New Notification",
      body: notification.body || notification.request?.content?.body || "",
      type: notification.data?.type || "general",
      read: false,
      timestamp: Date.now(),
      data: notification.data || notification.request?.content?.data || {},
    };

    // Get existing notifications
    const existingNotificationsJson = await AsyncStorage.getItem(
      NOTIFICATIONS_STORAGE_KEY
    );
    const existingNotifications = existingNotificationsJson
      ? JSON.parse(existingNotificationsJson)
      : [];

    // Add new notification to the beginning of the array
    const updatedNotifications = [
      notificationToStore,
      ...existingNotifications,
    ];

    // Store updated notifications
    await AsyncStorage.setItem(
      NOTIFICATIONS_STORAGE_KEY,
      JSON.stringify(updatedNotifications)
    );
    console.log("Notification stored:", notificationToStore);

    return notificationToStore;
  } catch (error) {
    console.error("Error storing notification:", error);
    return null;
  }
}

/**
 * Get all stored notifications
 */
export async function getStoredNotifications() {
  try {
    const notificationsJson = await AsyncStorage.getItem(
      NOTIFICATIONS_STORAGE_KEY
    );
    return notificationsJson ? JSON.parse(notificationsJson) : [];
  } catch (error) {
    console.error("Error getting stored notifications:", error);
    return [];
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(notificationId) {
  try {
    const notificationsJson = await AsyncStorage.getItem(
      NOTIFICATIONS_STORAGE_KEY
    );
    const notifications = notificationsJson
      ? JSON.parse(notificationsJson)
      : [];

    const updatedNotifications = notifications.map((notification) =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    );

    await AsyncStorage.setItem(
      NOTIFICATIONS_STORAGE_KEY,
      JSON.stringify(updatedNotifications)
    );
    return true;
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return false;
  }
}

/**
 * Send a local notification for testing
 */
export async function sendTestNotification(title, body, data = {}) {
  try {
    // Generate a unique ID for this notification
    const notificationId = `test-${Date.now()}`;

    // Schedule the notification
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          ...data,
          notificationId, // Include the ID in the data
        },
      },
      trigger: null, // null means send immediately
    });

    console.log("Test notification sent:", {
      title,
      body,
      data,
      notificationId,
    });

    // We don't store test notifications here anymore
    // They will be stored when clicked, just like real notifications

    return true;
  } catch (error) {
    console.error("Error sending test notification:", error);
    return false;
  }
}
