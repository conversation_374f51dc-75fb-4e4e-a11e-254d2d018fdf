import axios from "axios";
import { store } from "../store";

// Create an axios instance
const api = axios.create({
  baseURL: "",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Get auth token from store if available
    const state = store.getState();
    const token = state.auth?.token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors here
    if (error.response) {
      // Server responded with a status code outside of 2xx range
      console.log("Error response:", error.response.status);
    } else if (error.request) {
      // Request was made but no response received
      console.log("Error request:", error.request);
    } else {
      // Something happened in setting up the request
      console.log("Error message:", error.message);
    }

    return Promise.reject(error);
  }
);

export { api };
