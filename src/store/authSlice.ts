import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import { saveUserData, saveLoginStatus, clearAuthData } from "../utils/storage";

// Define types for the user data
interface UserData {
  user_id: string;
  user_email: string;
  user_fullname: string;
  user_gender: string;
  user_address: string;
  user_fbid: string;
  app_secret: string;
  user_dob: string;
  user_cphone: string;
  td_user_id: string;
  user_city: string;
  payment_settings: any;
  token: string;
  refresh_token: string;
}

// Define the auth state
interface AuthState {
  isLoggedIn: boolean;
  userData: UserData | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: AuthState = {
  isLoggedIn: false,
  userData: null,
  loading: false,
  error: null,
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<UserData>) => {
      state.isLoggedIn = true;
      state.userData = action.payload;
      state.loading = false;
      state.error = null;

      // Save user data and login status to AsyncStorage
      saveUserData(action.payload);
      saveLoginStatus(true);
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      state.isLoggedIn = false;
      state.userData = null;

      // Clear auth data from AsyncStorage
      clearAuthData();
    },
    // New action to initialize state from AsyncStorage
    initializeAuth: (
      state,
      action: PayloadAction<{ isLoggedIn: boolean; userData: UserData | null }>
    ) => {
      state.isLoggedIn = action.payload.isLoggedIn;
      state.userData = action.payload.userData;
    },
  },
});

// Export actions and reducer
export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  initializeAuth,
} = authSlice.actions;
export default authSlice.reducer;
