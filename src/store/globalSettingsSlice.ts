// src/store/globalSettingsSlice.ts
import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

// Define types for the global settings state
interface GlobalSettingsState {
  minimumSpend: string;
  tax: string;
  discount: string;
  inventory: string;
  deliveryCharges: string;
  deliveryChargesTax: string;
  isLoading: boolean;
  error: string | null;
  settings: {
    coupon: {
      override: boolean;
    };
    editor: {
      enabled: boolean;
    };
    product: {
      show_out_of_stock_price: boolean;
    };
    gratuity: {
      type: number;
      label: string;
      value: number;
      pickup: boolean;
      enabled: boolean;
      delivery: boolean;
    };
  };
}

// Initial state
const initialState: GlobalSettingsState = {
  minimumSpend: "0",
  tax: "0",
  discount: "0",
  inventory: "0",
  deliveryCharges: "0",
  deliveryChargesTax: "0",
  isLoading: false,
  error: null,
  settings: {
    coupon: {
      override: false,
    },
    editor: {
      enabled: false,
    },
    product: {
      show_out_of_stock_price: false,
    },
    gratuity: {
      type: 0,
      label: "",
      value: 0,
      pickup: false,
      enabled: false,
      delivery: false,
    },
  },
};

// Create the global settings slice
const globalSettingsSlice = createSlice({
  name: "globalSettings",
  initialState,
  reducers: {
    fetchGlobalSettingsStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    fetchGlobalSettingsSuccess: (state, action: PayloadAction<any>) => {
      const { result } = action.payload;
      
      // Only update if status is "1"
      if (action.payload.status === "1") {
        state.minimumSpend = result.minimum_spend || "0";
        state.tax = result.tax || "0";
        state.discount = result.discount || "0";
        state.inventory = result.inventory || "0";
        state.deliveryCharges = result.delivery_charges || "0";
        state.deliveryChargesTax = result.delivery_charges_tax || "0";
        
        // Update settings if available
        if (result.settings) {
          state.settings = result.settings;
        }
      }
      
      state.isLoading = false;
    },
    fetchGlobalSettingsFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
  },
});

// Export actions and reducer
export const {
  fetchGlobalSettingsStart,
  fetchGlobalSettingsSuccess,
  fetchGlobalSettingsFailure,
} = globalSettingsSlice.actions;

export default globalSettingsSlice.reducer;
