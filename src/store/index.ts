import { configureStore } from "@reduxjs/toolkit";
import orderReducer from "./orderSlice/index";
import authReducer from "./authSlice";
import globalSettingsReducer from "./globalSettingsSlice";

const store = configureStore({
  reducer: {
    order: orderReducer,
    auth: authReducer,
    globalSettings: globalSettingsReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
export { store };
