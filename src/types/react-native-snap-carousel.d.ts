declare module 'react-native-snap-carousel' {
  import React from 'react';
  import { StyleProp, ViewStyle } from 'react-native';

  export interface CarouselProps<T> {
    data: T[];
    renderItem: (item: { item: T; index: number }) => React.ReactNode;
    sliderWidth: number;
    itemWidth: number;
    inactiveSlideScale?: number;
    inactiveSlideOpacity?: number;
    activeSlideAlignment?: 'center' | 'start' | 'end';
    onSnapToItem?: (index: number) => void;
    firstItem?: number;
    initialScrollIndex?: number;
    enableSnap?: boolean;
    enableMomentum?: boolean;
    lockScrollWhileSnapping?: boolean;
    removeClippedSubviews?: boolean;
    layout?: 'default' | 'stack' | 'tinder';
    loop?: boolean;
    loopClonesPerSide?: number;
    autoplay?: boolean;
    autoplayDelay?: number;
    autoplayInterval?: number;
    scrollEnabled?: boolean;
    style?: StyleProp<ViewStyle>;
    containerCustomStyle?: StyleProp<ViewStyle>;
    contentContainerCustomStyle?: StyleProp<ViewStyle>;
  }

  export default class Carousel<T> extends React.Component<CarouselProps<T>> {
    snapToItem: (index: number) => void;
    snapToNext: () => void;
    snapToPrev: () => void;
    currentIndex: number;
  }
}
