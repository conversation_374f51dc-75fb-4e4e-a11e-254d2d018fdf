import type { AxiosRequestConfig } from "axios";
import { Platform } from "react-native";
import moment from "moment";
import { BUSINESS_ID } from "../config";

// API endpoints
export const getCartCalculations = (): AxiosRequestConfig => ({
  url: `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/cart/calculate`,
  method: "POST",
});

export const getBusinessData = (
  businessId: string,
  branchId: string
): AxiosRequestConfig => ({
  url: `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${businessId}/locations/${branchId}`,
  method: "GET",
});

export const getPickupSlots = (branchId: string): AxiosRequestConfig => {
  // Format current date as YYYY-MM-DD
  const today = new Date();
  const formattedDate = today.toISOString().split("T")[0]; // Gets YYYY-MM-DD format

  console.log("Fetching pickup slots for date:", formattedDate);

  return {
    url: `https://tossdown.com/api/pickup_hours_time_slots?bid=${branchId}&date=${formattedDate}&type=pickup`,
    method: "GET",
  };
};

export const createOrder = (businessId: string): AxiosRequestConfig => ({
  url: `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${businessId}/order`,
  method: "POST",
});

//New cart API
export const getCart = (
  businessId: string,
  uniqueOrderId: string
): AxiosRequestConfig => ({
  url: `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${businessId}/cart/${uniqueOrderId}`,
  method: "GET",
});

// Helper function to create cart calculation payload
export const getCartCalculationPayload = (
  cartData: any,
  paymentType: string,
  orderType: string,
  couponCode = "",
  tip = 0
) => {
  console.log("Cart Calculation Input Parameters:", {
    cartData,
    paymentType,
    orderType,
    couponCode,
    tip,
  });

  // Ensure all required fields are present
  const items = (cartData.items || []).map((item: any) => ({
    menu_item_id: item.menu_item_id || "",
    product_name: item.dname || item.name || "",
    price: Number.parseFloat(item.dprice || item.price || 0),
    qty: Number.parseInt(item.dqty || item.quantity || 0),

    item_category_id: Number(item.category_id || 0),
    category_name: item.category_name || "",
    item_brand_id: Number(item.brand_id || 0),

    tax: Number.parseFloat(item.item_level_tax_value || 0),
    tax_percentage: item.tax || "0",
    item_level_tax_value: Number.parseFloat(item.item_level_tax_value || 0),

    discount: Number.parseFloat(item.item_level_discount_value || 0),
    discount_percentage: item.discount || "0",
    item_level_discount_value: Number.parseFloat(
      item.item_level_discount_value || 0
    ),

    product_code: item.product_code || "",
    slug: `menu_item_id_${item.menu_item_id || ""}`,
    weight_value: Number.parseFloat(item.weight_value || 0),
    weight_unit: item.weight_unit || "",
    calculated_weight: Number.parseFloat(item.calculated_weight || 0),
    note: item.comment || "",

    options:
      typeof item.option_set === "string" && item.option_set
        ? JSON.parse(item.option_set)
        : item.options || {},
  }));

  return {
    data: {
      flag: false,
      name: cartData.name || "",
      email: cartData.email || "",
      phone: cartData.phone || "",

      country: orderType === "delivery" ? cartData.userCountry || "" : "",
      city: orderType === "delivery" ? cartData.userCity || "" : "",
      area: orderType === "delivery" ? cartData.userArea || "" : "",

      usr_lat: cartData.userLocation?.lat || "0",
      usr_lng: cartData.userLocation?.lng || "0",
      postal_code: cartData.userPostalCode || "",

      business_id: cartData.businessId || "",
      branch_id: cartData.branchId || "",

      order_type: orderType || "delivery",
      payment_type: Number(paymentType) ? 1 : 0,
      service_charges: "0",

      tax: Number.parseFloat(cartData.tax || 0),
      tax_value: Number.parseFloat(cartData.tax_value || 0),
      discount: Number.parseFloat(cartData.discount || 0),
      discount_value: Number.parseFloat(cartData.discount_value || 0),

      coupon_code: couponCode || "",
      tip: Number.parseFloat(tip || 0),

      sub_total: Number.parseFloat(cartData.total || cartData.subtotal || 0),
      grand_total: Number.parseFloat(
        cartData.gtotal || cartData.grandTotal || 0
      ),

      items: items,
    },
  };
};

// Helper function to create order payload
export const createOrderPayload = (cartData: any, orderDetails: any) => {
  // Determine platform for source
  const platform = Platform.OS === "ios" ? "ios_app" : "android_app";

  // Format current date for delivery
  const currentDate = new Date();
  const formattedDate =
    currentDate.toISOString().split("T")[0] +
    " " +
    currentDate.toTimeString().split(" ")[0].substring(0, 8);

  // Format pickup date and time if provided
  let deliveryDateTime = formattedDate;
  if (
    orderDetails.orderType === "pickup" &&
    orderDetails.pickupDate &&
    orderDetails.pickupTime
  ) {
    deliveryDateTime = `${orderDetails.pickupDate} ${moment(
      orderDetails.pickupTime,
      "h:mm A"
    ).format("HH:mm:ss")}`;
  }

  return {
    data: {
      temp_order_id: cartData.temp_order_id || "",
      orderid: cartData.orderid?.toString() || "0",
      td_order_id: "0",

      business_id: cartData.businessId || "",
      bid: cartData.branchId || "",
      uid: cartData.userId || orderDetails.userId || "0",

      name: orderDetails.name || "",
      email: orderDetails.email || "",
      mobile_phone: orderDetails.phone || "",
      cnic: "",

      city: orderDetails.city || "",
      area: orderDetails.area || "",
      address: orderDetails.address || "",
      user_latitude: orderDetails.userLocation?.lat || "0",
      user_longitude: orderDetails.userLocation?.lng || "0",
      postal_code: orderDetails.postalCode || "",
      note: orderDetails.notes || "",

      payment_type: Number(orderDetails.paymentMethod) || 0,
      source: platform,
      ordertype: orderDetails.orderType || "delivery",
      order_type: orderDetails.orderType || "delivery",
      order_type_flag: orderDetails.orderType === "delivery" ? "0" : "1",

      delivery: deliveryDateTime,
      delivery_date:
        orderDetails.orderType === "pickup" &&
        orderDetails.pickupDate &&
        orderDetails.pickupTime
          ? deliveryDateTime
          : "0000-00-00 00:00:00",

      discount: cartData.discount || "0",
      discount_value: cartData.discount_value || 0,
      tax: cartData.tax || "0",
      tax_value: cartData.tax_value || 0,
      delivery_charges: cartData.delivery_charges || 0,
      delivery_tax: cartData.delivery_tax || "0",
      delivery_tax_value: cartData.delivery_tax_value || 0,
      tip: cartData.tip || 0,
      service_charges: "0",

      loyalty_points: "0",
      canada_post: "0",

      custom_code: "",
      custom_code_id: "0",
      custom_code_type: "0",
      custom_code_discount: 0,
      custom_code_discount_value: 0,

      total: cartData.subtotal || cartData.total || 0,
      gtotal: cartData.grandTotal || cartData.grand_total || 0,

      items: (cartData.items || []).map((item: any) => ({
        id: item.menu_item_id || item.id,
        odetailid: item.odetailid || 0,
        orderid: item.orderid || cartData.orderid || "0",

        name: item.dname || item.product_name || item.name,
        qty: item.dqty || item.qty,
        price: item.dprice || item.price,
        total: item.dtotal || item.total,
        item_level_grand_total:
          item.item_level_grand_total || item.dtotal || item.total,
        comment: item.comment || item.note || "",

        category_id: item.category_id || item.item_category_id || 0,
        category_name: item.category_name || "null",
        brand_id: item.brand_id || item.item_brand_id || 0,
        product_code: item.product_code || "null",

        option_set:
          typeof item.option_set === "string" && item.option_set
            ? JSON.parse(item.option_set)
            : item.options || {},

        discount: item.discount || "0",
        item_level_discount_value: item.item_level_discount_value || 0,
        coupon_discount: item.coupon_discount || 0,
        coupon_discount_value: item.coupon_discount_value || 0,
        tax: item.tax || "0",
        item_level_tax_value: item.item_level_tax_value || 0,
        adjustment_amount: item.adjustment_amount || 0,
        weight_value: item.weight_value ? item.weight_value.toString() : "",
        weight_unit: item.weight_unit || "",
      })),
    },
  };
};

// Get global settings
export const getGlobalSettings = (businessId: string): AxiosRequestConfig => ({
  url: `https://tossdown.com/api/eatout_global_settings?restaurant_id=${businessId}`,
  method: "GET",
});

// Helper function to set cart calculation response
export const setCartCalculationResponse = (
  cartDetails: any,
  calculatedCartDetails: any
) => {
  console.log("Setting cart calculation response with:", calculatedCartDetails);

  // Ensure we have the calculated values
  const calculatedTotal = calculatedCartDetails?.total || 0;
  const calculatedGrandTotal = calculatedCartDetails?.grand_total || 0;
  const calculatedTax = calculatedCartDetails?.tax_value || 0;
  const calculatedDeliveryCharges =
    calculatedCartDetails?.delivery_charges || 0;
  const calculatedDiscount = calculatedCartDetails?.discount_value || 0;
  const calculatedCouponDiscount =
    calculatedCartDetails?.coupon_discount_value || 0;
  const calculatedTip = calculatedCartDetails?.tip || 0;

  // Create a new cart object with updated values
  const updatedCart = {
    ...cartDetails,

    // Set all total-related fields to ensure consistency
    total: calculatedTotal,
    gtotal: calculatedGrandTotal,
    subtotal: calculatedTotal,
    grandTotal: calculatedGrandTotal,

    tax: calculatedCartDetails?.tax || 0,
    tax_value: calculatedTax,

    discount: calculatedCartDetails?.discount || 0,
    discount_value: calculatedDiscount,

    coupon_discount: calculatedCartDetails?.coupon_discount || 0,
    coupon_discount_value: calculatedCouponDiscount,

    tip: calculatedTip,
    delivery_charges: calculatedDeliveryCharges,

    items: calculatedCartDetails?.items?.map((item: any, index: number) => ({
      ...cartDetails?.items[index],

      menu_item_id: item.menu_item_id,
      dname: item.product_name,
      dprice: item.price,
      dqty: item.qty,
      dtotal: item.total,

      category_id: item.item_category_id,
      brand_id: item.item_brand_id,

      tax: item.tax_percentage?.toString() || "0",
      item_level_tax_value: item.item_level_tax_value || 0,

      discount: item.discount_percentage?.toString() || "0",
      item_level_discount_value: item.item_level_discount_value || 0,

      coupon_discount: item.coupon_percentage,
      coupon_discount_value: item.coupon_discount,

      product_code: item.product_code,
      weight_value: item.weight_value,
      weight_unit: item.weight_unit,
      calculated_weight: item.calculated_weight || 0,
      comment: item.note,

      option_set: JSON.stringify(item.options),
    })),
  };

  console.log("Updated cart object:", updatedCart);
  return updatedCart;
};
