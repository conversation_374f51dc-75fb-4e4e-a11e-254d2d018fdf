/**
 * Helper functions for working with option sets
 */

/**
 * Determines if an option set is required based on min_quantity
 * @param optionSet The option set to check
 * @returns boolean indicating if the option set is required
 */
export const isOptionSetRequired = (optionSet: any): boolean => {
  return optionSet.min_quantity !== "0"
}

/**
 * Determines if an option set allows multiple selections
 * @param optionSet The option set to check
 * @returns boolean indicating if multiple selections are allowed
 */
export const isOptionSetMultiSelect = (optionSet: any): boolean => {
  return optionSet.quantity > 1 || optionSet.quantity === "0"
}

/**
 * Gets a human-readable description of the option set requirements
 * @param optionSet The option set to describe
 * @returns A string describing the selection requirements
 */
export const getOptionSetRequirementText = (optionSet: any): string => {
  const min = optionSet.min_quantity
  const max = optionSet.quantity
  const itemCount = optionSet.items.length

  if (min === "0") {
    return "Optional"
  }

  if (min === max && min !== "0") {
    return `Select exactly ${min}`
  }

  if (min !== "0" && (max === "0" || Number.parseInt(max) > Number.parseInt(min))) {
    return `Select at least ${min}`
  }

  return "Required"
}

/**
 * Formats selected options for display in the cart
 * @param options The selected options object
 * @param optionSets The available option sets
 * @returns Formatted options object for display
 */
export const formatOptionsForDisplay = (options: any, optionSets: any[]): any => {
  const formattedOptions = {}

  optionSets.forEach((optionSet) => {
    const selectedItems = options[optionSet.id]?.items || {}
    const optionItems = []

    optionSet.items.forEach((optItem) => {
      if (selectedItems[optItem.id]) {
        optionItems.push({
          name: optItem.name,
          price: optItem.price,
          quantity: selectedItems[optItem.id],
        })
      }
    })

    if (optionItems.length > 0) {
      formattedOptions[optionSet.name] = optionItems
    }
  })

  return formattedOptions
}

/**
 * Validates that all required options are selected
 * @param selectedOptions The current selected options
 * @param optionSets The available option sets
 * @returns Array of names of option sets with invalid selections
 */
export const validateOptionSelections = (selectedOptions: any, optionSets: any[]): string[] => {
  const invalidOptions = []

  optionSets.forEach((optionSet) => {
    const isRequired = isOptionSetRequired(optionSet)
    const selectedItems = selectedOptions[optionSet.id]?.items || {}
    const selectedCount = Object.keys(selectedItems).length

    // Check if required but nothing selected
    if (isRequired && selectedCount === 0) {
      invalidOptions.push(optionSet.name)
      return
    }

    // Check min/max constraints
    const min = Number.parseInt(optionSet.min_quantity || "0")
    const max = optionSet.quantity === "0" ? Number.POSITIVE_INFINITY : Number.parseInt(optionSet.quantity || "1")

    if ((min > 0 && selectedCount < min) || (max > 0 && selectedCount > max)) {
      invalidOptions.push(optionSet.name)
    }
  })

  return invalidOptions
}

