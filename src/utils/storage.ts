import AsyncStorage from "@react-native-async-storage/async-storage";

// Keys for storing data
const AUTH_USER_KEY = "auth_user_data";
const AUTH_STATUS_KEY = "auth_status";

// Save user data to AsyncStorage
export const saveUserData = async (userData: any) => {
  try {
    const jsonValue = JSON.stringify(userData);
    await AsyncStorage.setItem(AUTH_USER_KEY, jsonValue);
    console.log("User data saved to AsyncStorage");
  } catch (error) {
    console.error("Error saving user data to AsyncStorage:", error);
  }
};

// Save login status to AsyncStorage
export const saveLoginStatus = async (isLoggedIn: boolean) => {
  try {
    await AsyncStorage.setItem(AUTH_STATUS_KEY, JSON.stringify(isLoggedIn));
    console.log("Login status saved to AsyncStorage:", isLoggedIn);
  } catch (error) {
    console.error("Error saving login status to AsyncStorage:", error);
  }
};

// Load user data from AsyncStorage
export const loadUserData = async () => {
  try {
    const jsonValue = await AsyncStorage.getItem(AUTH_USER_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    console.error("Error loading user data from AsyncStorage:", error);
    return null;
  }
};

// Load login status from AsyncStorage
export const loadLoginStatus = async () => {
  try {
    const value = await AsyncStorage.getItem(AUTH_STATUS_KEY);
    return value != null ? JSON.parse(value) : false;
  } catch (error) {
    console.error("Error loading login status from AsyncStorage:", error);
    return false;
  }
};

// Clear all auth data from AsyncStorage
export const clearAuthData = async () => {
  try {
    await AsyncStorage.multiRemove([AUTH_USER_KEY, AUTH_STATUS_KEY]);
    console.log("Auth data cleared from AsyncStorage");
  } catch (error) {
    console.error("Error clearing auth data from AsyncStorage:", error);
  }
};
